import dotenv from 'dotenv';
import DatabaseUserManager from './src/utils/databaseUserManager.js';
import EnvUserManager from './src/utils/envUserManager.js';

// Load environment variables
dotenv.config();

async function createDatabaseUsersForExistingEnvUsers() {
    console.log('🔄 Creating Database Users for Existing .env Users...\n');
    
    const envManager = new EnvUserManager();
    
    try {
        // Get all users from .env file
        const existingUsers = envManager.parseUsersFromEnv();
        console.log(`📊 Found ${existingUsers.length} existing users in .env file:`);
        
        for (const user of existingUsers) {
            console.log(`\n👤 Processing user: ${user.username} (${user.role})`);
            
            // Test if database user already exists
            const testResult = await DatabaseUserManager.testDatabaseUser(user.username, user.password);
            
            if (testResult.success) {
                console.log(`   ✅ Database user already exists and working: app_${user.username}`);
            } else {
                console.log(`   🔄 Creating database user: app_${user.username}`);
                
                // Create database user
                const createResult = await DatabaseUserManager.createDatabaseUser({
                    username: user.username,
                    password: user.password,
                    role: user.role
                });
                
                if (createResult.success) {
                    console.log(`   ✅ Database user created successfully: ${createResult.message}`);
                    
                    // Test the newly created user
                    const newTestResult = await DatabaseUserManager.testDatabaseUser(user.username, user.password);
                    console.log(`   📊 Connection test: ${newTestResult.success ? '✅' : '❌'} ${newTestResult.message}`);
                } else {
                    console.log(`   ❌ Failed to create database user: ${createResult.message}`);
                }
            }
        }
        
        console.log('\n' + '=' .repeat(50));
        console.log('🎉 Database User Creation Summary');
        console.log('=' .repeat(50));
        
        // Final verification
        for (const user of existingUsers) {
            const finalTest = await DatabaseUserManager.testDatabaseUser(user.username, user.password);
            console.log(`👤 ${user.username} (${user.role}): ${finalTest.success ? '✅' : '❌'} ${finalTest.message}`);
        }
        
    } catch (error) {
        console.error('❌ Database user creation failed:', error.message);
        console.error(error.stack);
    }
}

// Also create a function to list all database users
async function listDatabaseUsers() {
    console.log('\n🔍 Listing All Database Users...\n');
    
    try {
        const result = await DatabaseUserManager.listDatabaseUsers();
        
        if (result.success) {
            console.log(`📊 Found ${result.users.length} database users:`);
            result.users.forEach(user => {
                console.log(`   👤 ${user.User}@${user.Host}`);
            });
        } else {
            console.log(`❌ Failed to list database users: ${result.message}`);
        }
        
    } catch (error) {
        console.error('❌ Error listing database users:', error.message);
    }
}

// Main execution
async function main() {
    await createDatabaseUsersForExistingEnvUsers();
    await listDatabaseUsers();
}

// Run the script
main();
