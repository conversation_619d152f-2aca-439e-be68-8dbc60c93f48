import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
    Box, Plus, Search, Edit, Trash2, Eye, X, 
    Package, Tag, FileText, Save, AlertCircle, Layers 
} from 'lucide-react';
import { materialApi, supplierApi } from '../../services/api.js';

const MaterialManagement = () => {
    const [materials, setMaterials] = useState([]);
    const [suppliers, setSuppliers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedMaterial, setSelectedMaterial] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [formData, setFormData] = useState({
        material_name: '',
        category: 'Coffee Beans',
        type: 'eating',
        description: '',
        unit_of_measure: 'kg',
        supplier_id: ''
    });
    const [errors, setErrors] = useState({});
    const [submitLoading, setSubmitLoading] = useState(false);

    const materialTypes = ['eating', 'object'];
    const eatingCategories = ['Coffee Beans', 'Tea Leaves', 'Milk', 'Sugar', 'Syrup', 'Spices'];
    const objectCategories = ['Cups', 'Lids', 'Straws', 'Napkins', 'Bags', 'Utensils'];
    const units = ['kg', 'g', 'L', 'ml', 'pieces', 'boxes', 'packets'];

    useEffect(() => {
        fetchMaterials();
        fetchSuppliers();
    }, [currentPage, searchTerm]);

    const fetchMaterials = async () => {
        try {
            setLoading(true);
            const response = await materialApi.getAll({
                page: currentPage,
                limit: 10,
                search: searchTerm
            });
            
            if (response.success) {
                setMaterials(response.data);
                setTotalPages(response.pagination?.totalPages || 1);
            }
        } catch (error) {
            console.error('Error fetching materials:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchSuppliers = async () => {
        try {
            const response = await supplierApi.getAll({ limit: 500 });
            if (response.success) {
                setSuppliers(response.data);
            }
        } catch (error) {
            console.error('Error fetching suppliers:', error);
        }
    };

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    const openModal = (type, material = null) => {
        setModalType(type);
        setSelectedMaterial(material);
        setShowModal(true);
        setErrors({});
        
        if (type === 'create') {
            setFormData({
                material_name: '',
                category: 'Coffee Beans',
                type: 'eating',
                description: '',
                unit_of_measure: 'kg',
                supplier_id: ''
            });
        } else if (type === 'edit' && material) {
            setFormData({
                material_name: material.material_name || '',
                category: material.category || 'Coffee Beans',
                type: material.type || 'eating',
                description: material.description || '',
                unit_of_measure: material.unit_of_measure || 'kg',
                supplier_id: material.supplier_id || ''
            });
        }
    };

    const closeModal = () => {
        setShowModal(false);
        setSelectedMaterial(null);
        setFormData({
            material_name: '',
            category: 'Coffee Beans',
            type: 'eating',
            description: '',
            unit_of_measure: 'kg',
            supplier_id: ''
        });
        setErrors({});
    };

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.material_name.trim()) {
            newErrors.material_name = 'Material name is required';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        try {
            setSubmitLoading(true);
            
            const submitData = {
                ...formData,
                supplier_id: formData.supplier_id || null
            };
            
            if (modalType === 'create') {
                await materialApi.create(submitData);
            } else if (modalType === 'edit') {
                await materialApi.update(selectedMaterial.material_id, submitData);
            }
            
            await fetchMaterials();
            closeModal();
        } catch (error) {
            console.error('Error saving material:', error);
            setErrors({ submit: error.message || 'An error occurred while saving' });
        } finally {
            setSubmitLoading(false);
        }
    };

    const handleDelete = async (materialId) => {
        if (!window.confirm('Are you sure you want to delete this material?')) {
            return;
        }
        
        try {
            await materialApi.delete(materialId);
            await fetchMaterials();
        } catch (error) {
            console.error('Error deleting material:', error);
            alert('Error deleting material: ' + (error.message || 'Unknown error'));
        }
    };

    const getTypeColor = (type) => {
        switch (type) {
            case 'eating': return 'bg-green-100 text-green-800';
            case 'object': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getCategoryColor = (category) => {
        const eatingColors = {
            'Coffee Beans': 'bg-amber-100 text-amber-800',
            'Tea Leaves': 'bg-green-100 text-green-800',
            'Milk': 'bg-blue-100 text-blue-800',
            'Sugar': 'bg-pink-100 text-pink-800',
            'Syrup': 'bg-purple-100 text-purple-800',
            'Spices': 'bg-orange-100 text-orange-800'
        };
        
        const objectColors = {
            'Cups': 'bg-gray-100 text-gray-800',
            'Lids': 'bg-slate-100 text-slate-800',
            'Straws': 'bg-yellow-100 text-yellow-800',
            'Napkins': 'bg-red-100 text-red-800',
            'Bags': 'bg-indigo-100 text-indigo-800',
            'Utensils': 'bg-teal-100 text-teal-800'
        };
        
        return eatingColors[category] || objectColors[category] || 'bg-gray-100 text-gray-800';
    };

    const handleTypeChange = (type) => {
        setFormData({
            ...formData,
            type,
            category: type === 'eating' ? 'Coffee Beans' : 'Cups'
        });
    };

    const getAvailableCategories = () => {
        return formData.type === 'eating' ? eatingCategories : objectCategories;
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                    <Box className="w-8 h-8 text-gray-600" />
                    <h2 className="text-2xl font-bold text-gray-900">Material Management</h2>
                </div>
                <button
                    onClick={() => openModal('create')}
                    className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center space-x-2 transition-colors"
                >
                    <Plus className="w-4 h-4" />
                    <span>Add Material</span>
                </button>
            </div>

            {/* Search */}
            <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                        type="text"
                        placeholder="Search materials by name or category..."
                        value={searchTerm}
                        onChange={handleSearch}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                    />
                </div>
            </div>

            {/* Material Table */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                {loading ? (
                    <div className="flex items-center justify-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-500"></div>
                    </div>
                ) : (
                    <>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Material
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Type & Category
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Unit & Supplier
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description
                                        </th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {materials.map((material) => (
                                        <tr key={material.material_id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                        <Package className="w-5 h-5 text-gray-600" />
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {material.material_name}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            ID: {material.material_id}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900 flex items-center">
                                                    <Layers className="w-4 h-4 mr-2 text-gray-400" />
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(material.type)}`}>
                                                        {material.type}
                                                    </span>
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center mt-1">
                                                    <Tag className="w-4 h-4 mr-2 text-gray-400" />
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(material.category)}`}>
                                                        {material.category}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    Unit: {material.unit_of_measure}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    Supplier: {material.supplier_id ? `#${material.supplier_id}` : 'Not assigned'}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="text-sm text-gray-900 flex items-start">
                                                    <FileText className="w-4 h-4 mr-2 text-gray-400 mt-0.5" />
                                                    <span className="line-clamp-2">
                                                        {material.description || 'No description available'}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <button
                                                        onClick={() => openModal('view', material)}
                                                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                        title="View"
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => openModal('edit', material)}
                                                        className="text-green-600 hover:text-green-900 p-1 rounded"
                                                        title="Edit"
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => handleDelete(material.material_id)}
                                                        className="text-red-600 hover:text-red-900 p-1 rounded"
                                                        title="Delete"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                                <div className="flex-1 flex justify-between sm:hidden">
                                    <button
                                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                        disabled={currentPage === 1}
                                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                        disabled={currentPage === totalPages}
                                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Next
                                    </button>
                                </div>
                                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p className="text-sm text-gray-700">
                                            Page <span className="font-medium">{currentPage}</span> of{' '}
                                            <span className="font-medium">{totalPages}</span>
                                        </p>
                                    </div>
                                    <div>
                                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                            <button
                                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                                disabled={currentPage === 1}
                                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Previous
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                                disabled={currentPage === totalPages}
                                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Next
                                            </button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* Modal */}
            <AnimatePresence>
                {showModal && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                    >
                        <motion.div
                            initial={{ scale: 0.95, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.95, opacity: 0 }}
                            className="bg-white rounded-lg shadow-xl w-full max-w-lg"
                        >
                            <div className="flex justify-between items-center p-6 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {modalType === 'view' ? 'Material Details' :
                                     modalType === 'edit' ? 'Edit Material' : 'Create Material'}
                                </h3>
                                <button
                                    onClick={closeModal}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>

                            <div className="p-6">
                                {modalType === 'view' && selectedMaterial ? (
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Material Name</label>
                                            <p className="text-gray-900">{selectedMaterial.material_name}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(selectedMaterial.type)}`}>
                                                {selectedMaterial.type}
                                            </span>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(selectedMaterial.category)}`}>
                                                {selectedMaterial.category}
                                            </span>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Unit of Measure</label>
                                            <p className="text-gray-900">{selectedMaterial.unit_of_measure}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Supplier</label>
                                            <p className="text-gray-900">{selectedMaterial.supplier_id ? `Supplier #${selectedMaterial.supplier_id}` : 'Not assigned'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                            <p className="text-gray-900">{selectedMaterial.description || 'No description available'}</p>
                                        </div>
                                    </div>
                                ) : (
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        {errors.submit && (
                                            <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center">
                                                <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                                                <span className="text-red-700 text-sm">{errors.submit}</span>
                                            </div>
                                        )}

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Material Name *
                                            </label>
                                            <input
                                                type="text"
                                                value={formData.material_name}
                                                onChange={(e) => setFormData({...formData, material_name: e.target.value})}
                                                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent ${
                                                    errors.material_name ? 'border-red-300' : 'border-gray-300'
                                                }`}
                                                placeholder="Enter material name"
                                            />
                                            {errors.material_name && (
                                                <p className="text-red-500 text-xs mt-1">{errors.material_name}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Type *
                                                </label>
                                                <select
                                                    value={formData.type}
                                                    onChange={(e) => handleTypeChange(e.target.value)}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                                                >
                                                    {materialTypes.map(type => (
                                                        <option key={type} value={type}>
                                                            {type.charAt(0).toUpperCase() + type.slice(1)}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Category *
                                                </label>
                                                <select
                                                    value={formData.category}
                                                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                                                >
                                                    {getAvailableCategories().map(category => (
                                                        <option key={category} value={category}>{category}</option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Unit of Measure *
                                                </label>
                                                <select
                                                    value={formData.unit_of_measure}
                                                    onChange={(e) => setFormData({...formData, unit_of_measure: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                                                >
                                                    {units.map(unit => (
                                                        <option key={unit} value={unit}>{unit}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Supplier (Optional)
                                                </label>
                                                <select
                                                    value={formData.supplier_id}
                                                    onChange={(e) => setFormData({...formData, supplier_id: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                                                >
                                                    <option value="">Select supplier</option>
                                                    {suppliers.map(supplier => (
                                                        <option key={supplier.supplier_id} value={supplier.supplier_id}>
                                                            {supplier.supplier_name}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Description
                                            </label>
                                            <textarea
                                                value={formData.description}
                                                onChange={(e) => setFormData({...formData, description: e.target.value})}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                                                placeholder="Enter material description"
                                                rows="3"
                                            />
                                        </div>

                                        <div className="flex justify-end space-x-3 pt-4">
                                            <button
                                                type="button"
                                                onClick={closeModal}
                                                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={submitLoading}
                                                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 flex items-center space-x-2 transition-colors"
                                            >
                                                {submitLoading ? (
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                                ) : (
                                                    <Save className="w-4 h-4" />
                                                )}
                                                <span>{modalType === 'edit' ? 'Update' : 'Create'}</span>
                                            </button>
                                        </div>
                                    </form>
                                )}
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export default MaterialManagement;
