import os
import subprocess
from datetime import datetime
from dotenv import load_dotenv

# Database credentials
load_dotenv()

host = os.getenv("DB_HOST")
port = int(os.getenv("DB_PORT") or "3306")  # Default to 3306 if not found
user = os.getenv("DB_USER")
password = os.getenv("DB_PASSWORD")
database = os.getenv("DB_NAME")

# Backup directory - use raw string or forward slashes
backup_dir = r"D:\Year2\Term3\DataBase-\ProjectDB-Year2-Term3\BackupRecovery"
if not os.path.exists(backup_dir):
    os.makedirs(backup_dir)

# Generate backup file name with timestamp
timestamp = datetime.now().strftime("date_%Y-%m-%d_time_%H-%M-%S")
backup_file = os.path.join(backup_dir, f"{database}_backup_{timestamp}.sql")

# MySQL dump command
dump_command = f"mysqldump --host={host} --port={port} --user={user} --password={password} {database} > {backup_file}"

# Execute the dump command
try:
    subprocess.run(dump_command, shell=True, check=True)
    print(f"Backup successful: {backup_file}")
except subprocess.CalledProcessError as e:
    print(f"Error occurred during backup: {e}")

# Add these debug lines after load_dotenv()
load_dotenv()

print(f"DB_HOST: {os.getenv('DB_HOST')}")
print(f"DB_PORT: {os.getenv('DB_PORT')}")
print(f"DB_USER: {os.getenv('DB_USER')}")
print(f"DB_NAME: {os.getenv('DB_NAME')}")

# ...existing code...