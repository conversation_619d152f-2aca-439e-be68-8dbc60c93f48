import os
import subprocess
from datetime import datetime

# Database credentials
host = "coffe-management-db1st-choengrayu307-607c.b.aivencloud.com"
port = 21011
user = "avnadmin"
password = "AVNS_KiWXNwxtH4tpuvcCgbt"
database = "coffee_management_db"

# Backup directory
backup_dir = "D:\Year2\Term3\DataBase-\ProjectDB-Year2-Term3\BackupRecovery"
if not os.path.exists(backup_dir):
    os.makedirs(backup_dir)

# Generate backup file name with timestamp
timestamp = datetime.now().strftime("date_%Y-%m-%d_time_%H-%M-%S")
backup_file = os.path.join(backup_dir, f"{database}_backup_{timestamp}.sql")

# MySQL dump command
dump_command = f"mysqldump --host={host} --port={port} --user={user} --password={password} {database} > {backup_file}"

# Execute the dump command
try:
    subprocess.run(dump_command, shell=True, check=True)
    print(f"Backup successful: {backup_file}")
except subprocess.CalledProcessError as e:
    print(f"Error occurred during backup: {e}")