import dotenv from 'dotenv';
import { api } from '../Frontend/src/services/api.js';

dotenv.config();

// Test all analytics endpoints
async function testAnalyticsEndpoints() {
    console.log('🧪 Testing Analytics Endpoints...\n');
    
    const endpoints = [
        '/analytics/loyalty-points',
        '/analytics/material-costs',
        '/analytics/zero-quantity-products',
        '/analytics/sales/recent?days=7',
        '/analytics/products/most-sold',
        '/analytics/suppliers/spending',
        '/analytics/customers/order-history',
        '/analytics/sales/monthly',
        '/analytics/revenue/by-product',
        '/analytics/customers/top-spenders?limit=5',
        '/analytics/products/unused',
        '/analytics/products/most-frequent',
        '/analytics/orders/monthly-stats',
        '/analytics/orders/no-payment',
        '/analytics/materials/stock-levels',
        '/analytics/products/raw-material-cost'
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`Testing: ${endpoint}`);
            const response = await fetch(`http://localhost:3000/api${endpoint}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ ${endpoint} - Success: ${data.data?.length || 0} records`);
            } else {
                console.log(`❌ ${endpoint} - Error: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            console.log(`❌ ${endpoint} - Error: ${error.message}`);
        }
        console.log('');
    }
}

// Run tests
testAnalyticsEndpoints().catch(console.error);
