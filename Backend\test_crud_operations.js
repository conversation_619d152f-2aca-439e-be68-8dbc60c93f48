import dotenv from 'dotenv';
import DatabaseUserManager from './src/utils/databaseUserManager.js';
import EnvUserManager from './src/utils/envUserManager.js';

// Load environment variables
dotenv.config();

async function testUserCRUD() {
    console.log('🧪 Testing Complete User CRUD Operations...\n');
    
    const envManager = new EnvUserManager();
    const testUsername = 'testuser_' + Date.now();
    const testPassword = 'test123';
    const initialRole = 'cashier';
    const updatedRole = 'manager';
    
    try {
        console.log('=' .repeat(50));
        console.log('🔵 CREATE Operation');
        console.log('=' .repeat(50));
        
        // 1. CREATE - Test creating user in both .env and database
        console.log(`Creating user: ${testUsername} with role: ${initialRole}`);
        
        const createResult = await envManager.addUser({
            username: testUsername,
            password: testPassword,
            role: initialRole,
            name: 'Test User'
        });
        
        console.log('📊 Create Result:', createResult);
        
        if (createResult.success) {
            console.log('✅ User created successfully in .env file and database');
            
            // Test database connection
            const testConnection = await DatabaseUserManager.testDatabaseUser(testUsername, testPassword);
            console.log('📊 Database Connection Test:', testConnection);
        }
        
        console.log('\n' + '=' .repeat(50));
        console.log('🔵 READ Operation');
        console.log('=' .repeat(50));
        
        // 2. READ - Test reading user from .env file
        const users = envManager.parseUsersFromEnv();
        const createdUser = users.find(u => u.username === testUsername);
        
        if (createdUser) {
            console.log('✅ User found in .env file:', createdUser);
        } else {
            console.log('❌ User not found in .env file');
        }
        
        console.log('\n' + '=' .repeat(50));
        console.log('🔵 UPDATE Operation');
        console.log('=' .repeat(50));
        
        // 3. UPDATE - Test updating user role and password
        console.log(`Updating user: ${testUsername} to role: ${updatedRole}`);
        
        const updateResult = await envManager.updateUser(testUsername, {
            role: updatedRole,
            password: 'newpassword123'
        });
        
        console.log('📊 Update Result:', updateResult);
        
        if (updateResult.success) {
            console.log('✅ User updated successfully in .env file and database');
            
            // Test database connection with new password
            const testUpdatedConnection = await DatabaseUserManager.testDatabaseUser(testUsername, 'newpassword123');
            console.log('📊 Updated Database Connection Test:', testUpdatedConnection);
        }
        
        console.log('\n' + '=' .repeat(50));
        console.log('🔵 DELETE Operation');
        console.log('=' .repeat(50));
        
        // 4. DELETE - Test deleting user from both .env and database
        console.log(`Deleting user: ${testUsername}`);
        
        const deleteResult = await envManager.deleteUser(testUsername);
        
        console.log('📊 Delete Result:', deleteResult);
        
        if (deleteResult.success) {
            console.log('✅ User deleted successfully from .env file and database');
            
            // Verify user is gone from .env file
            const usersAfterDelete = envManager.parseUsersFromEnv();
            const deletedUser = usersAfterDelete.find(u => u.username === testUsername);
            
            if (!deletedUser) {
                console.log('✅ User successfully removed from .env file');
            } else {
                console.log('❌ User still exists in .env file');
            }
        }
        
        console.log('\n' + '=' .repeat(50));
        console.log('🔵 VERIFICATION - Test Existing Users');
        console.log('=' .repeat(50));
        
        // 5. VERIFICATION - Test existing users from .env file
        const existingUsers = envManager.parseUsersFromEnv();
        console.log(`📊 Found ${existingUsers.length} existing users:`);
        
        for (const user of existingUsers) {
            console.log(`\n👤 Testing user: ${user.username} (${user.role})`);
            
            // Test database connection for existing user
            const userTest = await DatabaseUserManager.testDatabaseUser(user.username, user.password);
            console.log(`   Database Connection: ${userTest.success ? '✅' : '❌'} ${userTest.message}`);
            
            // Test creating database user if it doesn't exist
            if (!userTest.success) {
                console.log(`   🔄 Creating database user for: ${user.username}`);
                const createDbUser = await DatabaseUserManager.createDatabaseUser({
                    username: user.username,
                    password: user.password,
                    role: user.role
                });
                console.log(`   📊 Database User Creation: ${createDbUser.success ? '✅' : '❌'} ${createDbUser.message}`);
            }
        }
        
        console.log('\n' + '=' .repeat(50));
        console.log('🎉 CRUD Test Summary');
        console.log('=' .repeat(50));
        console.log('✅ CREATE: User creation in .env file and database');
        console.log('✅ READ: User reading from .env file');
        console.log('✅ UPDATE: User update in .env file and database permissions');
        console.log('✅ DELETE: User deletion from .env file and database');
        console.log('✅ VERIFICATION: Existing users database connectivity');
        
    } catch (error) {
        console.error('❌ CRUD Test failed:', error.message);
        console.error(error.stack);
    }
}

// Run the test
testUserCRUD();
