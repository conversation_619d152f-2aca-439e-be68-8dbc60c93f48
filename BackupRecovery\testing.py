import subprocess

def backup_database(host, user, password, db_name, output_file):
    command = [
        "mysqldump",
        f"--host={host}",
        f"--user={user}",
        f"--password={password}",
        db_name,
        f"--result-file={output_file}"
    ]
    print(command)
    
    result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    print(result)
    
    if result.returncode == 0:
        print(f"Backup successful. File saved as {output_file}.")
    else:
        print(f"Error: {result.stderr.decode('utf-8')}")

# Example usage
backup_database("localhost", "root", "12345", "employee_db", "schedule_ran.sql")