import { fileURLToPath } from 'url';
import { dirname } from 'path';
import dotenv from 'dotenv';
import EnvUserManager from './src/utils/envUserManager.js';
import DatabaseUserManager from './src/utils/databaseUserManager.js';
import AuthService from './src/services/authService.js';
import { testRoleConnection } from './src/config/RoleBasedDBConnection.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Test script for dual authentication system
 */
class DualAuthTest {
    constructor() {
        this.envUserManager = new EnvUserManager();
        this.testResults = [];
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'test' ? '🧪' : 'ℹ️';
        console.log(`[${timestamp}] ${prefix} ${message}`);
    }

    async runTest(testName, testFunction) {
        this.log(`Running test: ${testName}`, 'test');
        try {
            const result = await testFunction();
            this.testResults.push({ test: testName, success: true, result });
            this.log(`Test passed: ${testName}`, 'success');
            return result;
        } catch (error) {
            this.testResults.push({ test: testName, success: false, error: error.message });
            this.log(`Test failed: ${testName} - ${error.message}`, 'error');
            throw error;
        }
    }

    async testDatabaseConnections() {
        return await this.runTest('Database Role Connections', async () => {
            const roles = ['admin', 'manager', 'cashier', 'vendor', 'barista', 'user'];
            const results = {};
            
            for (const role of roles) {
                const connected = await testRoleConnection(role);
                results[role] = connected;
                this.log(`Role ${role}: ${connected ? 'Connected' : 'Failed'}`);
            }
            
            return results;
        });
    }

    async testEnvUserManagement() {
        return await this.runTest('Environment User Management', async () => {
            // Test parsing existing users
            const existingUsers = this.envUserManager.parseUsersFromEnv();
            this.log(`Found ${existingUsers.length} existing users`);
            
            return { userCount: existingUsers.length, users: existingUsers };
        });
    }

    async testUserCreation() {
        return await this.runTest('User Creation (Dual Auth)', async () => {
            const testUserData = {
                username: 'test_user_' + Date.now(),
                password: 'test123',
                role: 'cashier',
                name: 'Test User'
            };

            this.log(`Creating test user: ${testUserData.username}`);
            
            // Test user creation
            const result = await this.envUserManager.addUser(testUserData);
            
            if (!result.success) {
                throw new Error(`User creation failed: ${result.message}`);
            }

            this.log(`User created successfully in .env file and database`);
            
            // Test database user connection
            const dbTest = await DatabaseUserManager.testDatabaseUser(testUserData.username, testUserData.password);
            
            if (!dbTest.success) {
                this.log(`Database user test failed: ${dbTest.message}`, 'error');
            } else {
                this.log(`Database user test passed`);
            }

            // Clean up - delete the test user
            await this.envUserManager.deleteUser(testUserData.username);
            this.log(`Test user cleaned up`);
            
            return { 
                envCreation: result.success,
                dbTest: dbTest.success,
                cleanup: true
            };
        });
    }

    async testAuthentication() {
        return await this.runTest('Authentication Service', async () => {
            // Test admin authentication
            const adminAuth = await AuthService.authenticate('admin', 'admin123');
            
            if (!adminAuth) {
                throw new Error('Admin authentication failed');
            }

            this.log(`Admin authentication successful`);
            
            return { 
                adminAuth: !!adminAuth,
                adminRole: adminAuth.role,
                hasDbConnection: !adminAuth.error
            };
        });
    }

    async testDatabaseUserManager() {
        return await this.runTest('Database User Manager', async () => {
            // Test listing database users
            const userList = await DatabaseUserManager.listDatabaseUsers();
            
            if (!userList.success) {
                throw new Error(`Failed to list database users: ${userList.message}`);
            }

            this.log(`Found ${userList.users.length} database users`);
            
            return {
                userCount: userList.users.length,
                users: userList.users
            };
        });
    }

    async runAllTests() {
        console.log('\n🧪 Starting Dual Authentication System Tests\n');
        console.log('='.repeat(50));

        try {
            await this.testDatabaseConnections();
            await this.testEnvUserManagement();
            await this.testAuthentication();
            await this.testDatabaseUserManager();
            await this.testUserCreation();

            console.log('\n' + '='.repeat(50));
            console.log('📊 TEST SUMMARY');
            console.log('='.repeat(50));

            const passedTests = this.testResults.filter(r => r.success).length;
            const totalTests = this.testResults.length;

            this.testResults.forEach(result => {
                const status = result.success ? '✅ PASS' : '❌ FAIL';
                console.log(`${status} - ${result.test}`);
                if (!result.success) {
                    console.log(`    Error: ${result.error}`);
                }
            });

            console.log('\n' + '='.repeat(50));
            console.log(`📈 Overall Result: ${passedTests}/${totalTests} tests passed`);
            
            if (passedTests === totalTests) {
                console.log('🎉 All tests passed! Dual authentication system is working correctly.');
            } else {
                console.log('⚠️  Some tests failed. Please check the configuration and try again.');
            }
            
            console.log('='.repeat(50));

        } catch (error) {
            console.log('\n❌ Test suite failed:', error.message);
        }
    }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new DualAuthTest();
    tester.runAllTests().catch(console.error);
}

export default DualAuthTest;
