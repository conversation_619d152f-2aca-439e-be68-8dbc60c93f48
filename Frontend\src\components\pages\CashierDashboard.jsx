import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
    ShoppingBag, DollarSign, Coffee, CreditCard, Users, Clock,
    Plus, Edit, Trash2, Search, Filter, Calendar, Star, TrendingUp
} from 'lucide-react';

const CashierDashboard = () => {
    const [activeOrders, setActiveOrders] = useState([
        { id: 1, customer: '<PERSON>', items: ['Latte', 'Croissant'], total: 8.50, status: 'pending' },
        { id: 2, customer: '<PERSON>', items: ['Espresso', '<PERSON>ie'], total: 5.25, status: 'preparing' },
        { id: 3, customer: '<PERSON>', items: ['Cappuccino'], total: 4.75, status: 'ready' },
    ]);

    const [todayStats] = useState({
        totalOrders: 45,
        totalRevenue: 287.50,
        avgOrderValue: 6.39,
        popularItem: 'Latte'
    });

    const [quickActions] = useState([
        { id: 1, title: 'New Order', icon: Plus, color: 'emerald', action: 'create_order' },
        { id: 2, title: 'View Menu', icon: Coffee, color: 'blue', action: 'view_menu' },
        { id: 3, title: 'Process Payment', icon: CreditCard, color: 'purple', action: 'process_payment' },
        { id: 4, title: 'Customer Queue', icon: Users, color: 'orange', action: 'view_queue' },
    ]);

    const getStatusColor = (status) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'preparing': return 'bg-blue-100 text-blue-800';
            case 'ready': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'pending': return <Clock className="w-4 h-4" />;
            case 'preparing': return <Coffee className="w-4 h-4" />;
            case 'ready': return <Star className="w-4 h-4" />;
            default: return <Clock className="w-4 h-4" />;
        }
    };

    return (
        <div className="space-y-6">
            {/* Stats Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white rounded-xl shadow-sm p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Today's Orders</p>
                            <p className="text-2xl font-bold text-gray-800">{todayStats.totalOrders}</p>
                            <p className="text-sm text-green-600">+12% from yesterday</p>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                            <ShoppingBag className="w-6 h-6 text-white" />
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Revenue</p>
                            <p className="text-2xl font-bold text-gray-800">${todayStats.totalRevenue}</p>
                            <p className="text-sm text-green-600">+8% from yesterday</p>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <DollarSign className="w-6 h-6 text-white" />
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Avg Order Value</p>
                            <p className="text-2xl font-bold text-gray-800">${todayStats.avgOrderValue}</p>
                            <p className="text-sm text-green-600">+3% from yesterday</p>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <TrendingUp className="w-6 h-6 text-white" />
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Popular Item</p>
                            <p className="text-2xl font-bold text-gray-800">{todayStats.popularItem}</p>
                            <p className="text-sm text-green-600">Most ordered</p>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                            <Coffee className="w-6 h-6 text-white" />
                        </div>
                    </div>
                </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {quickActions.map((action) => {
                        const Icon = action.icon;
                        const colorClasses = {
                            emerald: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
                            blue: 'bg-gradient-to-r from-blue-500 to-blue-600',
                            purple: 'bg-gradient-to-r from-purple-500 to-purple-600',
                            orange: 'bg-gradient-to-r from-orange-500 to-orange-600',
                        };

                        return (
                            <motion.button
                                key={action.id}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
                            >
                                <div className="flex items-center space-x-3">
                                    <div className={`w-10 h-10 rounded-lg ${colorClasses[action.color]} flex items-center justify-center`}>
                                        <Icon className="w-5 h-5 text-white" />
                                    </div>
                                    <div>
                                        <h4 className="font-medium text-gray-800">{action.title}</h4>
                                        <p className="text-sm text-gray-600">Click to {action.title.toLowerCase()}</p>
                                    </div>
                                </div>
                            </motion.button>
                        );
                    })}
                </div>
            </div>

            {/* Active Orders */}
            <div className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-800">Active Orders</h3>
                    <div className="flex items-center space-x-2">
                        <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <Search className="w-5 h-5 text-gray-600" />
                        </button>
                        <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <Filter className="w-5 h-5 text-gray-600" />
                        </button>
                    </div>
                </div>

                <div className="space-y-3">
                    {activeOrders.map((order) => (
                        <motion.div
                            key={order.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                        >
                            <div className="flex items-center space-x-4">
                                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                    <span className="text-white font-medium text-sm">
                                        {order.customer.charAt(0)}
                                    </span>
                                </div>
                                <div>
                                    <h4 className="font-medium text-gray-800">{order.customer}</h4>
                                    <p className="text-sm text-gray-600">{order.items.join(', ')}</p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-4">
                                <div className="text-right">
                                    <p className="font-medium text-gray-800">${order.total}</p>
                                    <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                                        {getStatusIcon(order.status)}
                                        <span className="capitalize">{order.status}</span>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <button className="p-2 rounded-lg hover:bg-gray-200 transition-colors">
                                        <Edit className="w-4 h-4 text-gray-600" />
                                    </button>
                                    <button className="p-2 rounded-lg hover:bg-gray-200 transition-colors">
                                        <Trash2 className="w-4 h-4 text-red-600" />
                                    </button>
                                </div>
                            </div>
                        </motion.div>
                    ))}
                </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
                <div className="space-y-3">
                    <div className="flex items-center space-x-3 text-sm">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-gray-600">Order #1234 completed</span>
                        <span className="text-gray-400">2 minutes ago</span>
                    </div>
                    <div className="flex items-center space-x-3 text-sm">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-gray-600">New order received</span>
                        <span className="text-gray-400">5 minutes ago</span>
                    </div>
                    <div className="flex items-center space-x-3 text-sm">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-gray-600">Payment processed</span>
                        <span className="text-gray-400">8 minutes ago</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CashierDashboard;
