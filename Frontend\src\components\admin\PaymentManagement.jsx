import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
    CreditCard, Plus, Search, Edit, Trash2, Eye, X, 
    User, ShoppingBag, Calendar, DollarSign, Save, AlertCircle, CheckCircle 
} from 'lucide-react';
import { paymentApi, orderApi, customerApi } from '../../services/api.js';

const PaymentManagement = () => {
    const [payments, setPayments] = useState([]);
    const [orders, setOrders] = useState([]);
    const [customers, setCustomers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedPayment, setSelectedPayment] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [formData, setFormData] = useState({
        order_id: '',
        customer_id: '',
        amount: '',
        payment_method: 'Cash',
        payment_status: 'Completed'
    });
    const [errors, setErrors] = useState({});
    const [submitLoading, setSubmitLoading] = useState(false);

    const paymentMethods = ['Cash', 'Credit Card', 'Debit Card', 'Mobile Payment', 'Bank Transfer'];
    const paymentStatuses = ['Pending', 'Completed', 'Failed', 'Refunded'];

    useEffect(() => {
        fetchPayments();
        fetchOrders();
        fetchCustomers();
    }, [currentPage, searchTerm]);

    const fetchPayments = async () => {
        try {
            setLoading(true);
            const response = await paymentApi.getAll({
                page: currentPage,
                limit: 10,
                search: searchTerm
            });
            
            if (response.success) {
                setPayments(response.data);
                setTotalPages(response.pagination?.totalPages || 1);
            }
        } catch (error) {
            console.error('Error fetching payments:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchOrders = async () => {
        try {
            const response = await orderApi.getAll({ limit: 500 });
            if (response.success) {
                setOrders(response.data);
            }
        } catch (error) {
            console.error('Error fetching orders:', error);
        }
    };

    const fetchCustomers = async () => {
        try {
            const response = await customerApi.getAll({ limit: 500 });
            if (response.success) {
                setCustomers(response.data);
            }
        } catch (error) {
            console.error('Error fetching customers:', error);
        }
    };

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    const openModal = (type, payment = null) => {
        setModalType(type);
        setSelectedPayment(payment);
        setShowModal(true);
        setErrors({});
        
        if (type === 'create') {
            setFormData({
                order_id: '',
                customer_id: '',
                amount: '',
                payment_method: 'Cash',
                payment_status: 'Completed'
            });
        } else if (type === 'edit' && payment) {
            setFormData({
                order_id: payment.order_id || '',
                customer_id: payment.customer_id || '',
                amount: payment.amount || '',
                payment_method: payment.payment_method || 'Cash',
                payment_status: payment.payment_status || 'Completed'
            });
        }
    };

    const closeModal = () => {
        setShowModal(false);
        setSelectedPayment(null);
        setFormData({
            order_id: '',
            customer_id: '',
            amount: '',
            payment_method: 'Cash',
            payment_status: 'Completed'
        });
        setErrors({});
    };

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.amount || parseFloat(formData.amount) <= 0) {
            newErrors.amount = 'Valid amount is required';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        try {
            setSubmitLoading(true);
            
            const submitData = {
                ...formData,
                order_id: formData.order_id || null,
                customer_id: formData.customer_id || null
            };
            
            if (modalType === 'create') {
                await paymentApi.create(submitData);
            } else if (modalType === 'edit') {
                await paymentApi.update(selectedPayment.payment_id, submitData);
            }
            
            await fetchPayments();
            closeModal();
        } catch (error) {
            console.error('Error saving payment:', error);
            setErrors({ submit: error.message || 'An error occurred while saving' });
        } finally {
            setSubmitLoading(false);
        }
    };

    const handleDelete = async (paymentId) => {
        if (!window.confirm('Are you sure you want to delete this payment record?')) {
            return;
        }
        
        try {
            await paymentApi.delete(paymentId);
            await fetchPayments();
        } catch (error) {
            console.error('Error deleting payment:', error);
            alert('Error deleting payment: ' + (error.message || 'Unknown error'));
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'Completed': return 'bg-green-100 text-green-800';
            case 'Pending': return 'bg-yellow-100 text-yellow-800';
            case 'Failed': return 'bg-red-100 text-red-800';
            case 'Refunded': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getMethodColor = (method) => {
        switch (method) {
            case 'Cash': return 'bg-green-100 text-green-800';
            case 'Credit Card': return 'bg-blue-100 text-blue-800';
            case 'Debit Card': return 'bg-purple-100 text-purple-800';
            case 'Mobile Payment': return 'bg-orange-100 text-orange-800';
            case 'Bank Transfer': return 'bg-indigo-100 text-indigo-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const handleOrderChange = (orderId) => {
        const selectedOrder = orders.find(o => o.order_id === parseInt(orderId));
        if (selectedOrder) {
            const orderTotal = parseFloat(selectedOrder.unit_price || 0) * parseInt(selectedOrder.quantity || 0);
            setFormData({
                ...formData,
                order_id: orderId,
                customer_id: selectedOrder.customer_id || '',
                amount: orderTotal.toFixed(2)
            });
        } else {
            setFormData({
                ...formData,
                order_id: orderId,
                customer_id: '',
                amount: ''
            });
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                    <CreditCard className="w-8 h-8 text-green-600" />
                    <h2 className="text-2xl font-bold text-gray-900">Payment Management</h2>
                </div>
                <button
                    onClick={() => openModal('create')}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2 transition-colors"
                >
                    <Plus className="w-4 h-4" />
                    <span>Add Payment</span>
                </button>
            </div>

            {/* Search */}
            <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                        type="text"
                        placeholder="Search payments by order or customer..."
                        value={searchTerm}
                        onChange={handleSearch}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                </div>
            </div>

            {/* Payment Table */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                {loading ? (
                    <div className="flex items-center justify-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
                    </div>
                ) : (
                    <>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Payment Details
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Order & Customer
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Amount & Method
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status & Date
                                        </th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {payments.map((payment) => (
                                        <tr key={payment.payment_id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                                        <CreditCard className="w-5 h-5 text-green-600" />
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            Payment #{payment.payment_id}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            ID: {payment.payment_id}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900 flex items-center">
                                                    <ShoppingBag className="w-4 h-4 mr-2 text-gray-400" />
                                                    {payment.order_id ? `Order #${payment.order_id}` : 'Direct Payment'}
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center mt-1">
                                                    <User className="w-4 h-4 mr-2 text-gray-400" />
                                                    {payment.customer_id ? `Customer #${payment.customer_id}` : 'Walk-in Customer'}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900 flex items-center">
                                                    <DollarSign className="w-4 h-4 mr-1 text-gray-400" />
                                                    ${parseFloat(payment.amount || 0).toFixed(2)}
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center mt-1">
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMethodColor(payment.payment_method)}`}>
                                                        {payment.payment_method}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900 flex items-center">
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.payment_status)}`}>
                                                        {payment.payment_status}
                                                    </span>
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center mt-1">
                                                    <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                                                    {new Date(payment.payment_date).toLocaleDateString()}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <button
                                                        onClick={() => openModal('view', payment)}
                                                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                        title="View"
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => openModal('edit', payment)}
                                                        className="text-green-600 hover:text-green-900 p-1 rounded"
                                                        title="Edit"
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => handleDelete(payment.payment_id)}
                                                        className="text-red-600 hover:text-red-900 p-1 rounded"
                                                        title="Delete"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                                <div className="flex-1 flex justify-between sm:hidden">
                                    <button
                                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                        disabled={currentPage === 1}
                                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                        disabled={currentPage === totalPages}
                                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Next
                                    </button>
                                </div>
                                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p className="text-sm text-gray-700">
                                            Page <span className="font-medium">{currentPage}</span> of{' '}
                                            <span className="font-medium">{totalPages}</span>
                                        </p>
                                    </div>
                                    <div>
                                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                            <button
                                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                                disabled={currentPage === 1}
                                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Previous
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                                disabled={currentPage === totalPages}
                                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Next
                                            </button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* Modal */}
            <AnimatePresence>
                {showModal && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                    >
                        <motion.div
                            initial={{ scale: 0.95, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.95, opacity: 0 }}
                            className="bg-white rounded-lg shadow-xl w-full max-w-lg"
                        >
                            <div className="flex justify-between items-center p-6 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {modalType === 'view' ? 'Payment Details' :
                                     modalType === 'edit' ? 'Edit Payment' : 'Create Payment'}
                                </h3>
                                <button
                                    onClick={closeModal}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>

                            <div className="p-6">
                                {modalType === 'view' && selectedPayment ? (
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Payment ID</label>
                                            <p className="text-gray-900">#{selectedPayment.payment_id}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Order</label>
                                            <p className="text-gray-900">{selectedPayment.order_id ? `Order #${selectedPayment.order_id}` : 'Direct Payment'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                                            <p className="text-gray-900">{selectedPayment.customer_id ? `Customer #${selectedPayment.customer_id}` : 'Walk-in Customer'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                                            <p className="text-gray-900 font-semibold text-lg">${parseFloat(selectedPayment.amount || 0).toFixed(2)}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMethodColor(selectedPayment.payment_method)}`}>
                                                {selectedPayment.payment_method}
                                            </span>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedPayment.payment_status)}`}>
                                                {selectedPayment.payment_status}
                                            </span>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
                                            <p className="text-gray-900">{new Date(selectedPayment.payment_date).toLocaleDateString()}</p>
                                        </div>
                                    </div>
                                ) : (
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        {errors.submit && (
                                            <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center">
                                                <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                                                <span className="text-red-700 text-sm">{errors.submit}</span>
                                            </div>
                                        )}

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Order (Optional)
                                            </label>
                                            <select
                                                value={formData.order_id}
                                                onChange={(e) => handleOrderChange(e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                            >
                                                <option value="">Direct Payment (No Order)</option>
                                                {orders.map(order => (
                                                    <option key={order.order_id} value={order.order_id}>
                                                        Order #{order.order_id} - ${(parseFloat(order.unit_price || 0) * parseInt(order.quantity || 0)).toFixed(2)}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Customer (Optional)
                                            </label>
                                            <select
                                                value={formData.customer_id}
                                                onChange={(e) => setFormData({...formData, customer_id: e.target.value})}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                            >
                                                <option value="">Walk-in Customer</option>
                                                {customers.map(customer => (
                                                    <option key={customer.customer_id} value={customer.customer_id}>
                                                        {customer.first_name} {customer.last_name} - {customer.email}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Amount *
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={formData.amount}
                                                onChange={(e) => setFormData({...formData, amount: e.target.value})}
                                                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                                                    errors.amount ? 'border-red-300' : 'border-gray-300'
                                                }`}
                                                placeholder="0.00"
                                            />
                                            {errors.amount && (
                                                <p className="text-red-500 text-xs mt-1">{errors.amount}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Payment Method *
                                                </label>
                                                <select
                                                    value={formData.payment_method}
                                                    onChange={(e) => setFormData({...formData, payment_method: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                                >
                                                    {paymentMethods.map(method => (
                                                        <option key={method} value={method}>{method}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Status *
                                                </label>
                                                <select
                                                    value={formData.payment_status}
                                                    onChange={(e) => setFormData({...formData, payment_status: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                                >
                                                    {paymentStatuses.map(status => (
                                                        <option key={status} value={status}>{status}</option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>

                                        <div className="flex justify-end space-x-3 pt-4">
                                            <button
                                                type="button"
                                                onClick={closeModal}
                                                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={submitLoading}
                                                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2 transition-colors"
                                            >
                                                {submitLoading ? (
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                                ) : (
                                                    <Save className="w-4 h-4" />
                                                )}
                                                <span>{modalType === 'edit' ? 'Update' : 'Create'}</span>
                                            </button>
                                        </div>
                                    </form>
                                )}
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export default PaymentManagement;
