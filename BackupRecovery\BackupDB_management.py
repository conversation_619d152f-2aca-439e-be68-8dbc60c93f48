import subprocess
from dotenv import load_dotenv
import os
load_dotenv()
host = os.getenv("DB_HOST")
user = os.getenv("DB_USER")
password = os.getenv("DB_PASSWORD")


def backup_database(host, user, password, db_name, output_file):
    command = [
        "mysqldump",
        f"--host={host}",
        f"--user={user}",
        f"--password={password}",
        db_name,
        f"--result-file={output_file}"
    ]
    print(command)
    
    result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    print(result)
    
    if result.returncode == 0:
        print(f"Backup successful. File saved as {output_file}.")
    else:
        print(f"Error: {result.stderr.decode('utf-8')}")


# Example usage
backup_database(host, user, password, "project_database_management", "project_database_management_backup.sql")
backup_database(host, user, password, "project_database_management1", "project_database_management1_backup.sql")
backup_database(host, user, password, "project_database_management2", "project_database_management2_backup.sql")