import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
    Activity, Search, Filter, Calendar, User, 
    Eye, Edit, Trash2, Plus, ShoppingBag, CreditCard,
    Users, Package, Truck, Box, Settings, LogIn, LogOut
} from 'lucide-react';
import { activityApi } from '../../services/api.js';

const ActivityLogs = () => {
    const [activities, setActivities] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState('all');
    const [filterUser, setFilterUser] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    const activityTypes = [
        'all', 'login', 'logout', 'create', 'update', 'delete', 
        'order', 'payment', 'inventory', 'system'
    ];

    useEffect(() => {
        fetchActivities();
    }, [currentPage, searchTerm, filterType, filterUser]);

    const fetchActivities = async () => {
        try {
            setLoading(true);
            const response = await activityApi.getAll({
                page: currentPage,
                limit: 20,
                search: searchTerm,
                type: filterType !== 'all' ? filterType : undefined,
                user: filterUser !== 'all' ? filterUser : undefined
            });
            
            if (response.success) {
                setActivities(response.data);
                setTotalPages(response.pagination?.totalPages || 1);
            }
        } catch (error) {
            console.error('Error fetching activities:', error);
            // Mock data for demonstration
            setActivities([
                {
                    id: 1,
                    user: 'John Admin',
                    action: 'Created new customer',
                    type: 'create',
                    entity: 'Customer',
                    entityId: 123,
                    timestamp: '2024-01-15T10:30:00Z',
                    ipAddress: '*************',
                    details: 'Created customer: Jane Smith (<EMAIL>)'
                },
                {
                    id: 2,
                    user: 'Sarah Manager',
                    action: 'Updated product price',
                    type: 'update',
                    entity: 'Product',
                    entityId: 45,
                    timestamp: '2024-01-15T10:25:00Z',
                    ipAddress: '*************',
                    details: 'Updated Cappuccino price from $4.50 to $4.75'
                },
                {
                    id: 3,
                    user: 'Mike Cashier',
                    action: 'Processed order',
                    type: 'order',
                    entity: 'Order',
                    entityId: 789,
                    timestamp: '2024-01-15T10:20:00Z',
                    ipAddress: '*************',
                    details: 'Order #789 - Total: $25.50'
                },
                {
                    id: 4,
                    user: 'John Admin',
                    action: 'User login',
                    type: 'login',
                    entity: 'User',
                    entityId: 1,
                    timestamp: '2024-01-15T09:00:00Z',
                    ipAddress: '*************',
                    details: 'Successful login'
                },
                {
                    id: 5,
                    user: 'System',
                    action: 'Low stock alert',
                    type: 'system',
                    entity: 'Inventory',
                    entityId: 12,
                    timestamp: '2024-01-15T08:45:00Z',
                    ipAddress: 'system',
                    details: 'Coffee Beans stock below minimum threshold'
                }
            ]);
            setTotalPages(1);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    const handleFilterType = (e) => {
        setFilterType(e.target.value);
        setCurrentPage(1);
    };

    const getActivityIcon = (type) => {
        switch (type) {
            case 'login': return LogIn;
            case 'logout': return LogOut;
            case 'create': return Plus;
            case 'update': return Edit;
            case 'delete': return Trash2;
            case 'order': return ShoppingBag;
            case 'payment': return CreditCard;
            case 'inventory': return Box;
            case 'system': return Settings;
            default: return Activity;
        }
    };

    const getActivityColor = (type) => {
        switch (type) {
            case 'login': return 'bg-green-100 text-green-600';
            case 'logout': return 'bg-gray-100 text-gray-600';
            case 'create': return 'bg-blue-100 text-blue-600';
            case 'update': return 'bg-yellow-100 text-yellow-600';
            case 'delete': return 'bg-red-100 text-red-600';
            case 'order': return 'bg-orange-100 text-orange-600';
            case 'payment': return 'bg-green-100 text-green-600';
            case 'inventory': return 'bg-purple-100 text-purple-600';
            case 'system': return 'bg-indigo-100 text-indigo-600';
            default: return 'bg-gray-100 text-gray-600';
        }
    };

    const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp);
        return {
            date: date.toLocaleDateString(),
            time: date.toLocaleTimeString()
        };
    };

    const getTimeAgo = (timestamp) => {
        const now = new Date();
        const past = new Date(timestamp);
        const diffInMinutes = Math.floor((now - past) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours}h ago`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays}d ago`;
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                    <Activity className="w-8 h-8 text-indigo-600" />
                    <h2 className="text-2xl font-bold text-gray-900">Activity Logs</h2>
                </div>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <input
                            type="text"
                            placeholder="Search activities..."
                            value={searchTerm}
                            onChange={handleSearch}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                        />
                    </div>
                    <div className="relative">
                        <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <select
                            value={filterType}
                            onChange={handleFilterType}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent appearance-none"
                        >
                            {activityTypes.map(type => (
                                <option key={type} value={type}>
                                    {type === 'all' ? 'All Activities' : type.charAt(0).toUpperCase() + type.slice(1)}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <select
                            value={filterUser}
                            onChange={(e) => setFilterUser(e.target.value)}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent appearance-none"
                        >
                            <option value="all">All Users</option>
                            <option value="admin">Admins</option>
                            <option value="manager">Managers</option>
                            <option value="cashier">Cashiers</option>
                            <option value="system">System</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Activity List */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                {loading ? (
                    <div className="flex items-center justify-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
                    </div>
                ) : (
                    <>
                        <div className="divide-y divide-gray-200">
                            {activities.map((activity) => {
                                const ActivityIcon = getActivityIcon(activity.type);
                                const timestamp = formatTimestamp(activity.timestamp);
                                
                                return (
                                    <motion.div
                                        key={activity.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        className="p-6 hover:bg-gray-50 transition-colors"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getActivityColor(activity.type)}`}>
                                                <ActivityIcon className="w-5 h-5" />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-2">
                                                        <p className="text-sm font-medium text-gray-900">
                                                            {activity.user}
                                                        </p>
                                                        <span className="text-sm text-gray-500">•</span>
                                                        <p className="text-sm text-gray-900">
                                                            {activity.action}
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="text-sm text-gray-500">
                                                            {getTimeAgo(activity.timestamp)}
                                                        </p>
                                                        <p className="text-xs text-gray-400">
                                                            {timestamp.date} {timestamp.time}
                                                        </p>
                                                    </div>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-1">
                                                    {activity.details}
                                                </p>
                                                <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                                    <span>Entity: {activity.entity}</span>
                                                    {activity.entityId && (
                                                        <span>ID: {activity.entityId}</span>
                                                    )}
                                                    <span>IP: {activity.ipAddress}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>
                                );
                            })}
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                                <div className="flex-1 flex justify-between sm:hidden">
                                    <button
                                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                        disabled={currentPage === 1}
                                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                        disabled={currentPage === totalPages}
                                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Next
                                    </button>
                                </div>
                                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p className="text-sm text-gray-700">
                                            Page <span className="font-medium">{currentPage}</span> of{' '}
                                            <span className="font-medium">{totalPages}</span>
                                        </p>
                                    </div>
                                    <div>
                                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                            <button
                                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                                disabled={currentPage === 1}
                                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Previous
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                                disabled={currentPage === totalPages}
                                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Next
                                            </button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default ActivityLogs;
