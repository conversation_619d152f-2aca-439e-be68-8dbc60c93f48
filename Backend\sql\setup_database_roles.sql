-- Database Setup Script for Role-Based Authentication
-- This script creates the roles and initial admin user for the Coffee Management System

-- =======================================================
-- 1. CREATE ROLES FOR APPLICATION USERS
-- =======================================================

-- Admin Role - Full access to all operations
CREATE ROLE IF NOT EXISTS 'admin_role';
GRANT ALL PRIVILEGES ON *.* TO 'admin_role' WITH GRANT OPTION;

-- Manager Role - Inventory and user management
CREATE ROLE IF NOT EXISTS 'inventory_manager_role';
GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management.* TO 'inventory_manager_role';
GRANT SELECT ON mysql.user TO 'inventory_manager_role';

-- Cashier Role - Order processing and customer management
CREATE ROLE IF NOT EXISTS 'order_processor_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management.customers TO 'order_processor_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management.orders TO 'order_processor_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management.order_items TO 'order_processor_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management.payments TO 'order_processor_role';
GRANT SELECT ON coffee_management.products TO 'order_processor_role';
GRANT SELECT ON coffee_management.loyalty_points TO 'order_processor_role';
GRANT UPDATE ON coffee_management.loyalty_points TO 'order_processor_role';

-- Warehouse Staff Role - Material and supplier management
CREATE ROLE IF NOT EXISTS 'warehouse_staff_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management.suppliers TO 'warehouse_staff_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management.materials TO 'warehouse_staff_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management.inventory_logs TO 'warehouse_staff_role';
GRANT SELECT ON coffee_management.products TO 'warehouse_staff_role';

-- Support Staff Role - Basic read access
CREATE ROLE IF NOT EXISTS 'support_staff_role';
GRANT SELECT ON coffee_management.customers TO 'support_staff_role';
GRANT SELECT ON coffee_management.orders TO 'support_staff_role';
GRANT SELECT ON coffee_management.order_items TO 'support_staff_role';
GRANT SELECT ON coffee_management.products TO 'support_staff_role';
GRANT SELECT ON coffee_management.loyalty_points TO 'support_staff_role';

-- =======================================================
-- 2. CREATE DEFAULT ADMIN USER
-- =======================================================

-- Create the default admin user that corresponds to the .env file
-- This user will be used to create other users
CREATE USER IF NOT EXISTS 'app_admin'@'%' IDENTIFIED BY 'admin123_db';
GRANT 'admin_role' TO 'app_admin'@'%';
SET DEFAULT ROLE 'admin_role' TO 'app_admin'@'%';

-- =======================================================
-- 3. FLUSH PRIVILEGES
-- =======================================================
FLUSH PRIVILEGES;

-- =======================================================
-- 4. VERIFICATION QUERIES
-- =======================================================

-- Check roles created
SELECT * FROM information_schema.applicable_roles WHERE ROLE_NAME LIKE '%_role';

-- Check users created
SELECT User, Host FROM mysql.user WHERE User LIKE 'app_%';

-- Check role assignments
SELECT * FROM mysql.role_edges WHERE FROM_USER LIKE 'app_%';

-- Display success message
SELECT 'Database roles and admin user created successfully!' AS Status;

-- =======================================================
-- 5. INSTRUCTIONS FOR CREATING ADDITIONAL USERS
-- =======================================================

/*
To create additional users through the application:

1. Login to the application as 'admin' with password 'admin123'
2. Navigate to the User Management section
3. Click "Create User"
4. Fill in the user details:
   - Username: Choose a unique username
   - Password: Set a secure password
   - Role: Select from available roles (admin, manager, cashier, vendor, barista, user)
   - Name: Full name of the user

The system will automatically:
- Store the user in the .env file for application authentication
- Create a database user with the format 'app_username'
- Assign the appropriate role to the database user
- Set up the correct permissions

Example users that can be created:
- Username: manager1, Role: manager
- Username: cashier1, Role: cashier
- Username: vendor1, Role: vendor
- Username: barista1, Role: barista
- Username: user1, Role: user

Each user will have a corresponding database user:
- app_manager1 with inventory_manager_role
- app_cashier1 with order_processor_role
- app_vendor1 with warehouse_staff_role
- app_barista1 with support_staff_role
- app_user1 with support_staff_role
*/

-- =======================================================
-- 6. ROLE PERMISSIONS SUMMARY
-- =======================================================

/*
admin_role:
- Full access to all tables and operations
- Can create, modify, and delete other users
- Can manage database permissions

inventory_manager_role:
- Full access to all application tables
- Can manage inventory, products, and materials
- Can view user information

order_processor_role:
- Can process customer orders
- Can manage customer information
- Can handle payments and loyalty points
- Read-only access to products

warehouse_staff_role:
- Can manage suppliers and materials
- Can update inventory logs
- Read-only access to products

support_staff_role:
- Read-only access to customer information
- Can view orders and products
- Can check loyalty points
*/
