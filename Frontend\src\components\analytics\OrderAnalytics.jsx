import React, { useState, useEffect } from 'react';
import { api } from '../../services/api.js';

const OrderAnalytics = () => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [orderData, setOrderData] = useState({
        monthlyStats: [],
        unpaidOrders: [],
        recentSales: []
    });

    useEffect(() => {
        fetchOrderData();
    }, []);

    const fetchOrderData = async () => {
        try {
            setLoading(true);
            const [
                monthlyStatsRes,
                unpaidOrdersRes,
                recentSalesRes
            ] = await Promise.all([
                api.get('/analytics/orders/monthly-stats'),
                api.get('/analytics/orders/no-payment'),
                api.get('/analytics/sales/recent?days=30')
            ]);

            setOrderData({
                monthlyStats: monthlyStatsRes.data.data || [],
                unpaidOrders: unpaidOrdersRes.data.data || [],
                recentSales: recentSalesRes.data.data || []
            });
        } catch (error) {
            console.error('Error fetching order data:', error);
            setError('Failed to load order analytics');
        } finally {
            setLoading(false);
        }
    };

    const StatCard = ({ title, value, subtitle, icon, color = 'blue' }) => (
        <div className={`bg-white rounded-lg shadow-md p-6 border-l-4 border-${color}-500`}>
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-2xl font-bold text-gray-900">{value}</p>
                    {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
                </div>
                <div className="text-3xl">{icon}</div>
            </div>
        </div>
    );

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                    <div className="text-red-500">⚠️</div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="text-sm text-red-700">{error}</div>
                    </div>
                </div>
            </div>
        );
    }

    const totalOrders = orderData.monthlyStats.reduce((sum, stat) => sum + parseInt(stat.total_orders), 0);
    const totalRevenue = orderData.monthlyStats.reduce((sum, stat) => sum + parseFloat(stat.total_revenue), 0);
    const avgOrderValue = orderData.monthlyStats.reduce((sum, stat) => sum + parseFloat(stat.average_order_value), 0) / orderData.monthlyStats.length;
    const unpaidOrdersValue = orderData.unpaidOrders.reduce((sum, order) => sum + (parseFloat(order.unit_price) * parseInt(order.quantity)), 0);

    return (
        <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                    title="Total Orders"
                    value={totalOrders.toLocaleString()}
                    subtitle="All time"
                    icon="📋"
                    color="blue"
                />
                <StatCard
                    title="Total Revenue"
                    value={`$${totalRevenue.toLocaleString()}`}
                    subtitle="All time"
                    icon="💰"
                    color="green"
                />
                <StatCard
                    title="Avg Order Value"
                    value={`$${avgOrderValue.toFixed(2)}`}
                    subtitle="Per order"
                    icon="📊"
                    color="purple"
                />
                <StatCard
                    title="Unpaid Orders"
                    value={orderData.unpaidOrders.length}
                    subtitle={`$${unpaidOrdersValue.toLocaleString()} value`}
                    icon="⚠️"
                    color="red"
                />
            </div>

            {/* Monthly Order Statistics */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Monthly Order Statistics</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Month
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Orders
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Revenue
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Avg Order Value
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Growth
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {orderData.monthlyStats.map((stat, index) => {
                                const prevStat = orderData.monthlyStats[index + 1];
                                const growth = prevStat ? 
                                    ((parseFloat(stat.total_revenue) - parseFloat(prevStat.total_revenue)) / parseFloat(prevStat.total_revenue) * 100).toFixed(1) : 
                                    null;
                                
                                return (
                                    <tr key={index} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {stat.month}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {parseInt(stat.total_orders).toLocaleString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${parseFloat(stat.total_revenue).toLocaleString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${parseFloat(stat.average_order_value).toFixed(2)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                                            {growth ? (
                                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                                    parseFloat(growth) > 0 
                                                        ? 'bg-green-100 text-green-800' 
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {growth > 0 ? '+' : ''}{growth}%
                                                </span>
                                            ) : (
                                                <span className="text-gray-500">-</span>
                                            )}
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Daily Sales Trend */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Daily Sales Trend (Last 30 Days)</h3>
                <div className="space-y-2">
                    {orderData.recentSales.map((sale, index) => {
                        const maxSale = Math.max(...orderData.recentSales.map(s => parseFloat(s.total_sales)));
                        const percentage = maxSale > 0 ? (parseFloat(sale.total_sales) / maxSale) * 100 : 0;
                        
                        return (
                            <div key={index} className="flex items-center space-x-3">
                                <div className="w-24 text-sm text-gray-600 text-right">
                                    {new Date(sale.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                                </div>
                                <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                                    <div
                                        className="bg-blue-500 h-6 rounded-full transition-all duration-300"
                                        style={{ width: `${Math.max(5, percentage)}%` }}
                                    />
                                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                                        ${parseFloat(sale.total_sales).toLocaleString()}
                                    </div>
                                </div>
                                <div className="w-16 text-sm text-gray-600">
                                    {sale.order_count || 0} orders
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* Unpaid Orders */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-red-600">Unpaid Orders</h3>
                    <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                        {orderData.unpaidOrders.length} orders
                    </span>
                </div>
                
                {orderData.unpaidOrders.length > 0 ? (
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Order ID
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Customer
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Product
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Quantity
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Order Date
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Days Overdue
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {orderData.unpaidOrders.map((order, index) => {
                                    const orderDate = new Date(order.order_date);
                                    const today = new Date();
                                    const daysOverdue = Math.floor((today - orderDate) / (1000 * 60 * 60 * 24));
                                    const totalAmount = parseFloat(order.unit_price) * parseInt(order.quantity);
                                    
                                    return (
                                        <tr key={index} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                #{order.order_id}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {order.first_name} {order.last_name}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {order.product_name}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {order.quantity}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ${totalAmount.toFixed(2)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {orderDate.toLocaleDateString()}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                                    daysOverdue > 30 
                                                        ? 'bg-red-100 text-red-800' 
                                                        : daysOverdue > 7 
                                                        ? 'bg-orange-100 text-orange-800' 
                                                        : 'bg-yellow-100 text-yellow-800'
                                                }`}>
                                                    {daysOverdue} days
                                                </span>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                ) : (
                    <div className="text-center py-8">
                        <div className="text-gray-500 text-lg">🎉</div>
                        <div className="text-gray-600 mt-2">All orders have been paid!</div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default OrderAnalytics;
