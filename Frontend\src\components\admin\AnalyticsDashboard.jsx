import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
    TrendingUp, DollarSign, ShoppingBag, Users, 
    Package, AlertTriangle, Calendar, BarChart3,
    PieChart, Activity, ArrowUp, ArrowDown
} from 'lucide-react';
import { analyticsApi } from '../../services/api.js';

const AnalyticsDashboard = () => {
    const [analytics, setAnalytics] = useState({
        overview: {
            totalRevenue: 0,
            totalOrders: 0,
            totalCustomers: 0,
            lowStockItems: 0
        },
        recentOrders: [],
        topProducts: [],
        revenueChart: [],
        stockAlerts: []
    });
    const [loading, setLoading] = useState(true);
    const [timeRange, setTimeRange] = useState('7d'); // 7d, 30d, 90d

    useEffect(() => {
        fetchAnalytics();
    }, [timeRange]);

    const fetchAnalytics = async () => {
        try {
            setLoading(true);
            const response = await analyticsApi.getDashboard({ timeRange });
            
            if (response.success) {
                setAnalytics(response.data);
            }
        } catch (error) {
            console.error('Error fetching analytics:', error);
            // Mock data for demonstration
            setAnalytics({
                overview: {
                    totalRevenue: 15420.50,
                    totalOrders: 342,
                    totalCustomers: 156,
                    lowStockItems: 8
                },
                recentOrders: [
                    { id: 1, customer: 'John Doe', amount: 25.50, status: 'Completed', date: '2024-01-15' },
                    { id: 2, customer: 'Jane Smith', amount: 18.75, status: 'Pending', date: '2024-01-15' },
                    { id: 3, customer: 'Walk-in', amount: 12.00, status: 'Completed', date: '2024-01-15' }
                ],
                topProducts: [
                    { name: 'Cappuccino', sales: 89, revenue: 445.00 },
                    { name: 'Latte', sales: 76, revenue: 380.00 },
                    { name: 'Americano', sales: 65, revenue: 260.00 }
                ],
                revenueChart: [
                    { date: '2024-01-09', revenue: 1200 },
                    { date: '2024-01-10', revenue: 1450 },
                    { date: '2024-01-11', revenue: 1100 },
                    { date: '2024-01-12', revenue: 1650 },
                    { date: '2024-01-13', revenue: 1800 },
                    { date: '2024-01-14', revenue: 1350 },
                    { date: '2024-01-15', revenue: 1920 }
                ],
                stockAlerts: [
                    { material: 'Coffee Beans', current: 5, minimum: 10, status: 'Low' },
                    { material: 'Milk', current: 2, minimum: 15, status: 'Critical' },
                    { material: 'Sugar', current: 8, minimum: 20, status: 'Low' }
                ]
            });
        } finally {
            setLoading(false);
        }
    };

    const StatCard = ({ title, value, icon: Icon, color, change, changeType }) => (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm p-6"
        >
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-2xl font-bold text-gray-900">{value}</p>
                    {change && (
                        <div className={`flex items-center mt-1 text-sm ${
                            changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                        }`}>
                            {changeType === 'positive' ? 
                                <ArrowUp className="w-4 h-4 mr-1" /> : 
                                <ArrowDown className="w-4 h-4 mr-1" />
                            }
                            {change}
                        </div>
                    )}
                </div>
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
                    <Icon className="w-6 h-6 text-white" />
                </div>
            </div>
        </motion.div>
    );

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'Completed': return 'bg-green-100 text-green-800';
            case 'Pending': return 'bg-yellow-100 text-yellow-800';
            case 'Critical': return 'bg-red-100 text-red-800';
            case 'Low': return 'bg-orange-100 text-orange-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                    <BarChart3 className="w-8 h-8 text-pink-600" />
                    <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
                </div>
                <div className="flex items-center space-x-2">
                    <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    >
                        <option value="7d">Last 7 days</option>
                        <option value="30d">Last 30 days</option>
                        <option value="90d">Last 90 days</option>
                    </select>
                </div>
            </div>

            {loading ? (
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500"></div>
                </div>
            ) : (
                <>
                    {/* Overview Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <StatCard
                            title="Total Revenue"
                            value={formatCurrency(analytics.overview.totalRevenue)}
                            icon={DollarSign}
                            color="bg-green-500"
                            change="+12.5%"
                            changeType="positive"
                        />
                        <StatCard
                            title="Total Orders"
                            value={analytics.overview.totalOrders.toLocaleString()}
                            icon={ShoppingBag}
                            color="bg-blue-500"
                            change="+8.2%"
                            changeType="positive"
                        />
                        <StatCard
                            title="Total Customers"
                            value={analytics.overview.totalCustomers.toLocaleString()}
                            icon={Users}
                            color="bg-purple-500"
                            change="+15.3%"
                            changeType="positive"
                        />
                        <StatCard
                            title="Low Stock Items"
                            value={analytics.overview.lowStockItems}
                            icon={AlertTriangle}
                            color="bg-red-500"
                            change="-2"
                            changeType="positive"
                        />
                    </div>

                    {/* Charts and Tables */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Revenue Chart */}
                        <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="bg-white rounded-lg shadow-sm p-6"
                        >
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
                                <TrendingUp className="w-5 h-5 text-green-500" />
                            </div>
                            <div className="h-64 flex items-end justify-between space-x-2">
                                {analytics.revenueChart.map((item, index) => {
                                    const maxRevenue = Math.max(...analytics.revenueChart.map(i => i.revenue));
                                    const height = (item.revenue / maxRevenue) * 100;
                                    return (
                                        <div key={index} className="flex flex-col items-center flex-1">
                                            <div
                                                className="bg-pink-500 rounded-t w-full transition-all duration-500 hover:bg-pink-600"
                                                style={{ height: `${height}%` }}
                                                title={`${new Date(item.date).toLocaleDateString()}: ${formatCurrency(item.revenue)}`}
                                            ></div>
                                            <span className="text-xs text-gray-500 mt-2">
                                                {new Date(item.date).toLocaleDateString('en-US', { weekday: 'short' })}
                                            </span>
                                        </div>
                                    );
                                })}
                            </div>
                        </motion.div>

                        {/* Top Products */}
                        <motion.div
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="bg-white rounded-lg shadow-sm p-6"
                        >
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">Top Products</h3>
                                <PieChart className="w-5 h-5 text-blue-500" />
                            </div>
                            <div className="space-y-4">
                                {analytics.topProducts.map((product, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                <Package className="w-4 h-4 text-blue-600" />
                                            </div>
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">{product.name}</p>
                                                <p className="text-xs text-gray-500">{product.sales} sales</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium text-gray-900">
                                                {formatCurrency(product.revenue)}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </motion.div>
                    </div>

                    {/* Recent Orders and Stock Alerts */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Recent Orders */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="bg-white rounded-lg shadow-sm p-6"
                        >
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">Recent Orders</h3>
                                <Activity className="w-5 h-5 text-orange-500" />
                            </div>
                            <div className="space-y-3">
                                {analytics.recentOrders.map((order) => (
                                    <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">{order.customer}</p>
                                            <p className="text-xs text-gray-500">
                                                {new Date(order.date).toLocaleDateString()}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium text-gray-900">
                                                {formatCurrency(order.amount)}
                                            </p>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                                                {order.status}
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </motion.div>

                        {/* Stock Alerts */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="bg-white rounded-lg shadow-sm p-6"
                        >
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">Stock Alerts</h3>
                                <AlertTriangle className="w-5 h-5 text-red-500" />
                            </div>
                            <div className="space-y-3">
                                {analytics.stockAlerts.map((alert, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div className="flex items-center space-x-3">
                                            <AlertTriangle className={`w-4 h-4 ${
                                                alert.status === 'Critical' ? 'text-red-500' : 'text-orange-500'
                                            }`} />
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">{alert.material}</p>
                                                <p className="text-xs text-gray-500">
                                                    Current: {alert.current} | Min: {alert.minimum}
                                                </p>
                                            </div>
                                        </div>
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(alert.status)}`}>
                                            {alert.status}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </motion.div>
                    </div>
                </>
            )}
        </div>
    );
};

export default AnalyticsDashboard;
