import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import {
    Coffee, Users, ShoppingBag, DollarSign, Activity, Shield, Settings,
    LogOut, Menu, X, TrendingUp, AlertTriangle, Eye, Clock, Star,
    BarChart3, PieChart, LineChart, Calendar, Filter, Search, Bell,
    Package, UserCheck, FileText, CreditCard, Truck, Box, ExternalLink
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../auth/RoleBasedAccess';
import { coffeeAPI } from '../services/api';
import UserManagement from '../admin/UserManagement';
import CustomerManagement from '../admin/CustomerManagement';
import ProductManagement from '../admin/ProductManagement';
import OrderManagement from '../admin/OrderManagement';
import SupplierManagement from '../admin/SupplierManagement';
import MaterialManagement from '../admin/MaterialManagement';
import PaymentManagement from '../admin/PaymentManagement';
import InventoryManagement from '../admin/InventoryManagement';
import AnalyticsDashboard from '../admin/AnalyticsDashboard';
import ActivityLogs from '../admin/ActivityLogs';
import CashierDashboard from './CashierDashboard';

const RoleBasedDashboard = () => {
    const { user, logout } = useAuth();
    const permissions = usePermissions();
    const navigate = useNavigate();
    const [sidebarOpen, setSidebarOpen] = useState(true);
    const [activeTab, setActiveTab] = useState('dashboard');
    const [dashboardData, setDashboardData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [notifications, setNotifications] = useState([
        { id: 1, message: "New order received", time: "2 min ago", type: "info" },
        { id: 2, message: "Low stock alert", time: "5 min ago", type: "warning" },
        { id: 3, message: "Payment processed", time: "10 min ago", type: "success" }
    ]);

    // Gradient classes per color for Tailwind
    const colorGradients = {
        emerald: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
        blue: 'bg-gradient-to-r from-blue-500 to-blue-600',
        purple: 'bg-gradient-to-r from-purple-500 to-purple-600',
        orange: 'bg-gradient-to-r from-orange-500 to-orange-600',
        pink: 'bg-gradient-to-r from-pink-500 to-pink-600',
        red: 'bg-gradient-to-r from-red-500 to-red-600',
        indigo: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
        gray: 'bg-gradient-to-r from-gray-500 to-gray-600',
    };

    // Role-based navigation items
    const getNavigationItems = () => {
        const baseItems = [
            { id: 'dashboard', label: 'Dashboard', icon: BarChart3, color: 'emerald' }
        ];

        // Admin gets full access
        if (permissions.isAdmin()) {
            return [
                ...baseItems,
                { id: 'users', label: 'User Management', icon: Users, color: 'blue' },
                { id: 'customers', label: 'Customers', icon: Users, color: 'green' },
                { id: 'employees', label: 'Employees', icon: UserCheck, color: 'purple' },
                { id: 'orders', label: 'Orders', icon: ShoppingBag, color: 'orange' },
                { id: 'payments', label: 'Payments', icon: CreditCard, color: 'green' },
                { id: 'products', label: 'Products', icon: Coffee, color: 'emerald' },
                { id: 'suppliers', label: 'Suppliers', icon: Truck, color: 'indigo' },
                { id: 'materials', label: 'Materials', icon: Package, color: 'gray' },
                { id: 'inventory', label: 'Inventory', icon: Box, color: 'gray' },
                { id: 'analytics', label: 'Analytics', icon: TrendingUp, color: 'pink' },
                { id: 'security', label: 'Security', icon: Shield, color: 'red' },
                { id: 'activity', label: 'Activity Logs', icon: Activity, color: 'indigo' },
                { id: 'settings', label: 'Settings', icon: Settings, color: 'gray' },
                { id: 'logout', label: 'Logout', icon: LogOut, color: 'red', action: 'logout' },
            ];
        }

        // Manager gets most access except user management
        if (permissions.isManager()) {
            return [
                ...baseItems,
                { id: 'customers', label: 'Customers', icon: Users, color: 'green' },
                { id: 'employees', label: 'Employees', icon: UserCheck, color: 'purple' },
                { id: 'orders', label: 'Orders', icon: ShoppingBag, color: 'orange' },
                { id: 'payments', label: 'Payments', icon: CreditCard, color: 'green' },
                { id: 'products', label: 'Products', icon: Coffee, color: 'emerald' },
                { id: 'suppliers', label: 'Suppliers', icon: Truck, color: 'indigo' },
                { id: 'materials', label: 'Materials', icon: Package, color: 'gray' },
                { id: 'inventory', label: 'Inventory', icon: Box, color: 'gray' },
                { id: 'analytics', label: 'Reports', icon: TrendingUp, color: 'pink' },
                { id: 'activity', label: 'Activity Logs', icon: Activity, color: 'indigo' },
                { id: 'logout', label: 'Logout', icon: LogOut, color: 'red', action: 'logout' },
            ];
        }

        // Cashier gets limited access
        if (permissions.isCashier()) {
            return [
                ...baseItems,
                { id: 'orders', label: 'Orders', icon: ShoppingBag, color: 'orange' },
                { id: 'products', label: 'Products', icon: Coffee, color: 'emerald' },
                { id: 'payments', label: 'Payments', icon: CreditCard, color: 'blue' },
                { id: 'inventory', label: 'Inventory', icon: Box, color: 'gray' },
                { id: 'logout', label: 'Logout', icon: LogOut, color: 'red', action: 'logout' },
            ];
        }

        // Regular user gets minimal access
        return [
            ...baseItems,
            { id: 'orders', label: 'My Orders', icon: ShoppingBag, color: 'orange' },
            { id: 'profile', label: 'Profile', icon: UserCheck, color: 'blue' },
            { id: 'logout', label: 'Logout', icon: LogOut, color: 'red', action: 'logout' },
        ];
    };

    const navigationItems = getNavigationItems();

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const fetchDashboardData = async () => {
        try {
            setLoading(true);
            const response = await coffeeAPI.getAnalytics();
            setDashboardData(response.data);
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleNavClick = (item) => {
        if (item.action === 'logout') {
            handleLogout();
        } else if (item.id === 'analytics') {
            navigate('/analytics');
        } else {
            setActiveTab(item.id);
        }
    };

    const handleLogout = async () => {
        try {
            await logout();
        } catch (error) {
            console.error('Logout error:', error);
        }
    };

    const renderContent = () => {
        // Special dashboard for cashiers
        if (activeTab === 'dashboard' && permissions.isCashier()) {
            return <CashierDashboard />;
        }

        switch (activeTab) {
            case 'dashboard':
                return <DashboardContent dashboardData={dashboardData} loading={loading} userRole={user?.role} />;
            case 'users':
                return permissions.canManageUsers() ? <UserManagement /> : <UnauthorizedAccess />;
            case 'customers':
                return permissions.canViewReports() ? <CustomerManagement /> : <UnauthorizedAccess />;
            case 'employees':
                return permissions.canViewReports() ? <EmployeeManagement /> : <UnauthorizedAccess />;
            case 'orders':
                return permissions.canProcessOrders() ? <OrderManagement /> : <UnauthorizedAccess />;
            case 'products':
                return permissions.canViewInventory() ? <ProductManagement /> : <UnauthorizedAccess />;
            case 'suppliers':
                return permissions.canViewReports() ? <SupplierManagement /> : <UnauthorizedAccess />;
            case 'materials':
                return permissions.canViewInventory() ? <MaterialManagement /> : <UnauthorizedAccess />;
            case 'payments':
                return permissions.canViewReports() ? <PaymentManagement /> : <UnauthorizedAccess />;
            case 'inventory':
                return permissions.canViewInventory() ? <InventoryManagement /> : <UnauthorizedAccess />;
            case 'analytics':
                return permissions.canViewReports() ? <AnalyticsDashboard /> : <UnauthorizedAccess />;
            case 'security':
                return permissions.isAdmin() ? <SecurityManagement /> : <UnauthorizedAccess />;
            case 'activity':
                return permissions.canViewReports() ? <ActivityLogs /> : <UnauthorizedAccess />;
            case 'settings':
                return permissions.isAdmin() ? <SettingsManagement /> : <UnauthorizedAccess />;
            case 'profile':
                return <ProfileManagement />;
            default:
                return <DashboardContent dashboardData={dashboardData} loading={loading} userRole={user?.role} />;
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 flex">
            {/* Sidebar */}
            <AnimatePresence>
                {sidebarOpen && (
                    <motion.div
                        initial={{ x: -280 }}
                        animate={{ x: 0 }}
                        exit={{ x: -280 }}
                        transition={{ type: "spring", stiffness: 300, damping: 30 }}
                        className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200 lg:relative lg:translate-x-0"
                    >
                        {/* Sidebar Header */}
                        <div className="flex items-center justify-between p-4 border-b border-gray-200">
                            <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                    <Coffee className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                    <h1 className="text-lg font-bold text-gray-800">Coffee Shop</h1>
                                    <p className="text-xs text-gray-500 capitalize">{user?.role} Dashboard</p>
                                </div>
                            </div>
                            <button
                                onClick={() => setSidebarOpen(false)}
                                className="lg:hidden p-1 rounded-lg hover:bg-gray-100 transition-colors"
                            >
                                <X className="w-5 h-5 text-gray-600" />
                            </button>
                        </div>

                        {/* User Info */}
                        <div className="p-4 border-b border-gray-200">
                            <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                    <span className="text-white font-medium">
                                        {user?.username?.charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-800">{user?.username}</p>
                                    <p className="text-xs text-gray-500 capitalize">{user?.role}</p>
                                </div>
                            </div>
                        </div>

                        {/* Navigation */}
                        <nav className="p-4 space-y-2">
                            {navigationItems.map((item) => {
                                const Icon = item.icon;
                                const isActive = activeTab === item.id;
                                const gradientClass = colorGradients[item.color] || colorGradients.gray;

                                return (
                                    <button
                                        key={item.id}
                                        onClick={() => handleNavClick(item)}
                                        className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group ${
                                            isActive
                                                ? `${gradientClass} text-white shadow-lg`
                                                : 'text-gray-600 hover:bg-gray-100'
                                        }`}
                                    >
                                        <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'}`} />
                                        <span className={`font-medium ${isActive ? 'text-white' : 'text-gray-700'}`}>
                                            {item.label}
                                        </span>
                                    </button>
                                );
                            })}
                        </nav>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Main Content */}
            <div className="flex-1 flex flex-col">
                {/* Header */}
                <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => setSidebarOpen(!sidebarOpen)}
                                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                            >
                                <Menu className="w-5 h-5 text-gray-600" />
                            </button>
                            <h2 className="text-xl font-semibold text-gray-800 capitalize">
                                {activeTab === 'dashboard' ? `${user?.role} Dashboard` : activeTab.replace(/([A-Z])/g, ' $1').trim()}
                            </h2>
                        </div>

                        <div className="flex items-center space-x-4">
                            <button className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors">
                                <Bell className="w-5 h-5 text-gray-600" />
                                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                            </button>
                            <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600">Welcome back,</span>
                                <span className="text-sm font-medium text-gray-800">{user?.username}</span>
                            </div>
                        </div>
                    </div>
                </header>

                {/* Content Area */}
                <main className="flex-1 p-6 overflow-y-auto">
                    {renderContent()}
                </main>
            </div>

            {/* Sidebar Overlay for Mobile */}
            {sidebarOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}
        </div>
    );
};

// Dashboard Content Component
const DashboardContent = ({ dashboardData, loading, userRole }) => {
    const navigate = useNavigate();
    
    const getRoleBadgeColor = (role) => {
        switch (role) {
            case 'admin': return 'bg-red-100 text-red-800';
            case 'manager': return 'bg-blue-100 text-blue-800';
            case 'cashier': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Welcome Section */}
            <div className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h3 className="text-2xl font-bold text-gray-800">Welcome to your dashboard!</h3>
                        <p className="text-gray-600 mt-1">Here's what's happening in your coffee shop today.</p>
                    </div>
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleBadgeColor(userRole)}`}>
                        {userRole?.charAt(0).toUpperCase() + userRole?.slice(1)}
                    </div>
                </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatsCard
                    title="Today's Orders"
                    value="24"
                    change="+12%"
                    icon={ShoppingBag}
                    color="emerald"
                />
                <StatsCard
                    title="Revenue"
                    value="$1,234"
                    change="+8%"
                    icon={DollarSign}
                    color="blue"
                />
                <StatsCard
                    title="Active Items"
                    value="156"
                    change="+3%"
                    icon={Coffee}
                    color="purple"
                />
                <StatsCard
                    title="Customers"
                    value="89"
                    change="+5%"
                    icon={Users}
                    color="orange"
                />
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm p-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <QuickActionCard
                        title="New Order"
                        description="Create a new order"
                        icon={ShoppingBag}
                        color="emerald"
                    />
                    <QuickActionCard
                        title="Analytics Dashboard"
                        description="View detailed analytics"
                        icon={BarChart3}
                        color="blue"
                        onClick={() => navigate('/analytics')}
                    />
                    <QuickActionCard
                        title="Inventory"
                        description="Manage inventory"
                        icon={Box}
                        color="purple"
                    />
                </div>
            </div>
        </div>
    );
};

// Stats Card Component
const StatsCard = ({ title, value, change, icon: Icon, color }) => {
    const colorGradients = {
        emerald: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
        blue: 'bg-gradient-to-r from-blue-500 to-blue-600',
        purple: 'bg-gradient-to-r from-purple-500 to-purple-600',
        orange: 'bg-gradient-to-r from-orange-500 to-orange-600',
    };

    return (
        <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm text-gray-600">{title}</p>
                    <p className="text-2xl font-bold text-gray-800 mt-1">{value}</p>
                    <p className="text-sm text-green-600 mt-1">{change} from yesterday</p>
                </div>
                <div className={`w-12 h-12 rounded-lg ${colorGradients[color]} flex items-center justify-center`}>
                    <Icon className="w-6 h-6 text-white" />
                </div>
            </div>
        </div>
    );
};

// Quick Action Card Component
const QuickActionCard = ({ title, description, icon: Icon, color, onClick }) => {
    const colorGradients = {
        emerald: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
        blue: 'bg-gradient-to-r from-blue-500 to-blue-600',
        purple: 'bg-gradient-to-r from-purple-500 to-purple-600',
    };

    return (
        <div 
            className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors cursor-pointer"
            onClick={onClick}
        >
            <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-lg ${colorGradients[color]} flex items-center justify-center`}>
                    <Icon className="w-5 h-5 text-white" />
                </div>
                <div>
                    <h5 className="font-medium text-gray-800">{title}</h5>
                    <p className="text-sm text-gray-600">{description}</p>
                </div>
            </div>
        </div>
    );
};

// Placeholder components for different sections
const UnauthorizedAccess = () => (
    <div className="flex items-center justify-center h-64">
        <div className="text-center">
            <Shield className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800">Access Denied</h3>
            <p className="text-gray-600">You don't have permission to access this section.</p>
        </div>
    </div>
);

const EmployeeManagement = () => (
    <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Employee Management</h3>
        <p className="text-gray-600">Employee management interface will be implemented here.</p>
    </div>
);

const InventoryManagement = () => (
    <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Inventory Management</h3>
        <p className="text-gray-600">Inventory management interface will be implemented here.</p>
    </div>
);

const PaymentManagement = () => (
    <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Payment Management</h3>
        <p className="text-gray-600">Payment management interface will be implemented here.</p>
    </div>
);

const AnalyticsContent = () => (
    <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Analytics & Reports</h3>
        <p className="text-gray-600">Analytics and reporting interface will be implemented here.</p>
    </div>
);

const SecurityManagement = () => (
    <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Security Management</h3>
        <p className="text-gray-600">Security management interface will be implemented here.</p>
    </div>
);

const ActivityLogs = () => (
    <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Activity Logs</h3>
        <p className="text-gray-600">Activity logs interface will be implemented here.</p>
    </div>
);

const SettingsManagement = () => (
    <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Settings</h3>
        <p className="text-gray-600">Settings management interface will be implemented here.</p>
    </div>
);

const ProfileManagement = () => (
    <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Profile Management</h3>
        <p className="text-gray-600">Profile management interface will be implemented here.</p>
    </div>
);

export default RoleBasedDashboard;
