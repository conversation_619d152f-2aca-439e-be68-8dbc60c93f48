{"name": "backend", "version": "1.0.0", "main": "AivenSQL.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "test-auth": "node test_dual_auth.js", "test-crud": "node test_crud_operations.js", "create-db-users": "node create_existing_db_users.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@faker-js/faker": "^9.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}}