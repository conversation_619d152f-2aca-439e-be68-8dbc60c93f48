-- drop database coffee_management_db;
CREATE DATABASE coffee_management_db;
USE coffee_management_db;


-- Customers Table
CREATE TABLE customers(
    customer_id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VA<PERSON><PERSON>R(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
-- Employees Table
CREATE TABLE employees(
    employee_id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    position ENUM('Manager', 'Cashier', 'Vendor', 'Barista') NOT NULL,
    hire_date DATE NOT NULL,
    salary DECIMAL(10, 2) CHECK (salary > 0),
    phone_number VA<PERSON>HAR(20) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    address VARCHAR(255) NOT NULL
);

create table suppliers(
	supplier_id int primary key auto_increment,
    supplies_name varchar(255),
    contact_email varchar(255),
    phone_number int not null,
    location_url varchar(255) not null,
    category enum('eating material', 'material object') not null default 'material object'
);

create table material_eating(
	material_eating_id int primary key auto_increment,
    supplier_id int,
    name_material varchar(250),
    quantity int not null check (quantity > 0),
    unit_price decimal(10,2) not null check (unit_price > 0), 
    import_date timestamp default CURRENT_TIMESTAMP not null,
    category ENUM(
	  'coffee bean',
	  'matcha',
	  'sugar',
	  'condensed milk',
	  'fresh milk',
	  'espresso',
	  'chocolate',
	  'green tea',
	  'black tea',
	  'caramel syrup',
	  'vanilla syrup',
	  'hazelnut syrup',
	  'ice',
	  'whipped cream',
	  'honey',
	  'almond milk',
	  'soy milk',
	  'coconut milk',
	  'mint',
	  'lemon juice'
	) NOT NULL,
    employee_id int,
    foreign key (supplier_id) references suppliers(supplier_id),
    foreign key (employee_id) references employees(employee_id)
);

create table material_object(
	material_object_id int primary key auto_increment,
    supplier_id int,
    unit_price decimal(10,2) not null check (unit_price > 0),
    name_material varchar(250),
    category ENUM(
	  'tissue',
	  'plastic cup',
	  'paper cup',
	  'cup lid',
	  'straw',
	  'plastic bag',
	  'napkin',
	  'glove',
	  'mask',
	  'tray',
	  'cleaning spray',
	  'dishwashing liquid',
	  'cloth towel',
	  'toilet paper',
	  'garbage bag',
	  'ice bucket',
	  'measuring spoon',
	  'milk frother',
	  'filter paper',
	  'receipt roll'
	) NOT NULL,
    quantity int not null check (quantity > 0),
    import_date timestamp default CURRENT_TIMESTAMP not null,
    employee_id int,
    foreign key (supplier_id) references suppliers(supplier_id),
    foreign key (employee_id) references employees(employee_id)
);

-- Products Table
CREATE TABLE products (
    product_id INT PRIMARY KEY AUTO_INCREMENT,
    product_name VARCHAR(100) NOT NULL,
    material_eating_id int,
    material_object_id int,
    category_name ENUM('Coffee', 'Tea', 'Pastry', 'Sandwich', 'Juice', 'Other') NOT NULL,
    description TEXT,
    unit_price DECIMAL(10, 2) NOT NULL CHECK (unit_price > 0),
    -- product_status enum('Available', 'Unavailable') NOT NULL DEFAULT 'Available',
    foreign key (material_object_id) references material_object(material_object_id),
    foreign key (material_eating_id) references material_eating(material_eating_id)
);

-- Orders Table
CREATE TABLE orders(
    order_id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT,   
    product_id INT NOT NULL,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    quantity INT NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10, 2) NOT NULL CHECK (unit_price > 0),
	status_order ENUM ('Pending', 'Completed') DEFAULT 'Pending',
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
);

-- Payments Table
CREATE TABLE payments(
    payment_id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    order_id INT NOT NULL UNIQUE,  -- Unique to prevent duplicate payments
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_type ENUM ('ABA', 'Aceleda', 'Cash', 'Other') DEFAULT 'Other',
    amount_money DECIMAL(10, 2) NOT NULL CHECK (amount_money > 0),
    FOREIGN KEY (employee_id) REFERENCES employees(employee_id),
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);

CREATE TABLE loyalty_points (
    point_id INT PRIMARY KEY AUTO_INCREMENT,
    -- customer_id INT NOT NULL,
    order_id int,
    points_earned INT NOT NULL CHECK (points_earned > 0),
    -- FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);

-- drop trigger if exists after_payment_insert;
-- drop trigger after_payment_insert;
    DELIMITER //

CREATE TRIGGER after_payment_insert
AFTER INSERT ON payments
FOR EACH ROW
BEGIN
    DECLARE order_qty INT;

    -- Get quantity from order
    SELECT quantity INTO order_qty
    FROM orders
    WHERE order_id = NEW.order_id;

    -- Insert loyalty points
    INSERT INTO loyalty_points (order_id, points_earned)
    VALUES (NEW.order_id, order_qty);
END;
//

DELIMITER ;
