import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
    Box, Plus, Search, Edit, Trash2, Eye, X, 
    Package, AlertTriangle, CheckCircle, Save, AlertCircle, TrendingUp, TrendingDown 
} from 'lucide-react';
import { inventoryApi, materialApi, supplierApi } from '../../services/api.js';

const InventoryManagement = () => {
    const [inventory, setInventory] = useState([]);
    const [materials, setMaterials] = useState([]);
    const [suppliers, setSuppliers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedItem, setSelectedItem] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [formData, setFormData] = useState({
        material_id: '',
        current_stock: '',
        minimum_stock: '',
        maximum_stock: '',
        reorder_point: '',
        last_restocked: '',
        supplier_id: ''
    });
    const [errors, setErrors] = useState({});
    const [submitLoading, setSubmitLoading] = useState(false);

    useEffect(() => {
        fetchInventory();
        fetchMaterials();
        fetchSuppliers();
    }, [currentPage, searchTerm]);

    const fetchInventory = async () => {
        try {
            setLoading(true);
            const response = await inventoryApi.getAll({
                page: currentPage,
                limit: 10,
                search: searchTerm
            });
            
            if (response.success) {
                setInventory(response.data);
                setTotalPages(response.pagination?.totalPages || 1);
            }
        } catch (error) {
            console.error('Error fetching inventory:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchMaterials = async () => {
        try {
            const response = await materialApi.getAll({ limit: 500 });
            if (response.success) {
                setMaterials(response.data);
            }
        } catch (error) {
            console.error('Error fetching materials:', error);
        }
    };

    const fetchSuppliers = async () => {
        try {
            const response = await supplierApi.getAll({ limit: 500 });
            if (response.success) {
                setSuppliers(response.data);
            }
        } catch (error) {
            console.error('Error fetching suppliers:', error);
        }
    };

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    const openModal = (type, item = null) => {
        setModalType(type);
        setSelectedItem(item);
        setShowModal(true);
        setErrors({});
        
        if (type === 'create') {
            setFormData({
                material_id: '',
                current_stock: '',
                minimum_stock: '',
                maximum_stock: '',
                reorder_point: '',
                last_restocked: new Date().toISOString().split('T')[0],
                supplier_id: ''
            });
        } else if (type === 'edit' && item) {
            setFormData({
                material_id: item.material_id || '',
                current_stock: item.current_stock || '',
                minimum_stock: item.minimum_stock || '',
                maximum_stock: item.maximum_stock || '',
                reorder_point: item.reorder_point || '',
                last_restocked: item.last_restocked ? item.last_restocked.split('T')[0] : '',
                supplier_id: item.supplier_id || ''
            });
        }
    };

    const closeModal = () => {
        setShowModal(false);
        setSelectedItem(null);
        setFormData({
            material_id: '',
            current_stock: '',
            minimum_stock: '',
            maximum_stock: '',
            reorder_point: '',
            last_restocked: '',
            supplier_id: ''
        });
        setErrors({});
    };

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.material_id) {
            newErrors.material_id = 'Material is required';
        }
        
        if (!formData.current_stock || parseFloat(formData.current_stock) < 0) {
            newErrors.current_stock = 'Valid current stock is required';
        }
        
        if (!formData.minimum_stock || parseFloat(formData.minimum_stock) < 0) {
            newErrors.minimum_stock = 'Valid minimum stock is required';
        }
        
        if (!formData.maximum_stock || parseFloat(formData.maximum_stock) < 0) {
            newErrors.maximum_stock = 'Valid maximum stock is required';
        }
        
        if (parseFloat(formData.minimum_stock) >= parseFloat(formData.maximum_stock)) {
            newErrors.maximum_stock = 'Maximum stock must be greater than minimum stock';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        try {
            setSubmitLoading(true);
            
            const submitData = {
                ...formData,
                supplier_id: formData.supplier_id || null
            };
            
            if (modalType === 'create') {
                await inventoryApi.create(submitData);
            } else if (modalType === 'edit') {
                await inventoryApi.update(selectedItem.inventory_id, submitData);
            }
            
            await fetchInventory();
            closeModal();
        } catch (error) {
            console.error('Error saving inventory item:', error);
            setErrors({ submit: error.message || 'An error occurred while saving' });
        } finally {
            setSubmitLoading(false);
        }
    };

    const handleDelete = async (inventoryId) => {
        if (!window.confirm('Are you sure you want to delete this inventory item?')) {
            return;
        }
        
        try {
            await inventoryApi.delete(inventoryId);
            await fetchInventory();
        } catch (error) {
            console.error('Error deleting inventory item:', error);
            alert('Error deleting inventory item: ' + (error.message || 'Unknown error'));
        }
    };

    const getStockStatus = (item) => {
        const current = parseFloat(item.current_stock || 0);
        const minimum = parseFloat(item.minimum_stock || 0);
        const reorder = parseFloat(item.reorder_point || 0);
        
        if (current <= 0) {
            return { status: 'Out of Stock', color: 'bg-red-100 text-red-800', icon: AlertTriangle };
        } else if (current <= reorder) {
            return { status: 'Reorder Required', color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle };
        } else if (current <= minimum) {
            return { status: 'Low Stock', color: 'bg-orange-100 text-orange-800', icon: TrendingDown };
        } else {
            return { status: 'In Stock', color: 'bg-green-100 text-green-800', icon: CheckCircle };
        }
    };

    const getStockPercentage = (item) => {
        const current = parseFloat(item.current_stock || 0);
        const maximum = parseFloat(item.maximum_stock || 1);
        return Math.min(100, (current / maximum) * 100);
    };

    const getMaterialName = (materialId) => {
        const material = materials.find(m => m.material_id === materialId);
        return material ? material.material_name : `Material #${materialId}`;
    };

    const getSupplierName = (supplierId) => {
        const supplier = suppliers.find(s => s.supplier_id === supplierId);
        return supplier ? supplier.supplier_name : 'Not assigned';
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                    <Box className="w-8 h-8 text-gray-600" />
                    <h2 className="text-2xl font-bold text-gray-900">Inventory Management</h2>
                </div>
                <button
                    onClick={() => openModal('create')}
                    className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center space-x-2 transition-colors"
                >
                    <Plus className="w-4 h-4" />
                    <span>Add Inventory Item</span>
                </button>
            </div>

            {/* Search */}
            <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                        type="text"
                        placeholder="Search inventory by material name..."
                        value={searchTerm}
                        onChange={handleSearch}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                    />
                </div>
            </div>

            {/* Inventory Table */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                {loading ? (
                    <div className="flex items-center justify-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-500"></div>
                    </div>
                ) : (
                    <>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Material
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Stock Levels
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Supplier & Last Restocked
                                        </th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {inventory.map((item) => {
                                        const stockStatus = getStockStatus(item);
                                        const stockPercentage = getStockPercentage(item);
                                        const StatusIcon = stockStatus.icon;
                                        
                                        return (
                                            <tr key={item.inventory_id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                            <Package className="w-5 h-5 text-gray-600" />
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {getMaterialName(item.material_id)}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                ID: {item.inventory_id}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        Current: <span className="font-medium">{item.current_stock}</span>
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        Min: {item.minimum_stock} | Max: {item.maximum_stock}
                                                    </div>
                                                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                                                        <div 
                                                            className={`h-2 rounded-full ${
                                                                stockPercentage <= 25 ? 'bg-red-500' :
                                                                stockPercentage <= 50 ? 'bg-yellow-500' :
                                                                'bg-green-500'
                                                            }`}
                                                            style={{ width: `${stockPercentage}%` }}
                                                        ></div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <StatusIcon className="w-4 h-4 mr-2" />
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>
                                                            {stockStatus.status}
                                                        </span>
                                                    </div>
                                                    {item.reorder_point && (
                                                        <div className="text-sm text-gray-500 mt-1">
                                                            Reorder at: {item.reorder_point}
                                                        </div>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        {getSupplierName(item.supplier_id)}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {item.last_restocked ? 
                                                            `Last: ${new Date(item.last_restocked).toLocaleDateString()}` : 
                                                            'Never restocked'
                                                        }
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <div className="flex items-center justify-end space-x-2">
                                                        <button
                                                            onClick={() => openModal('view', item)}
                                                            className="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                            title="View"
                                                        >
                                                            <Eye className="w-4 h-4" />
                                                        </button>
                                                        <button
                                                            onClick={() => openModal('edit', item)}
                                                            className="text-green-600 hover:text-green-900 p-1 rounded"
                                                            title="Edit"
                                                        >
                                                            <Edit className="w-4 h-4" />
                                                        </button>
                                                        <button
                                                            onClick={() => handleDelete(item.inventory_id)}
                                                            className="text-red-600 hover:text-red-900 p-1 rounded"
                                                            title="Delete"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                                <div className="flex-1 flex justify-between sm:hidden">
                                    <button
                                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                        disabled={currentPage === 1}
                                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                        disabled={currentPage === totalPages}
                                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Next
                                    </button>
                                </div>
                                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p className="text-sm text-gray-700">
                                            Page <span className="font-medium">{currentPage}</span> of{' '}
                                            <span className="font-medium">{totalPages}</span>
                                        </p>
                                    </div>
                                    <div>
                                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                            <button
                                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                                disabled={currentPage === 1}
                                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Previous
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                                disabled={currentPage === totalPages}
                                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Next
                                            </button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* Modal */}
            <AnimatePresence>
                {showModal && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                    >
                        <motion.div
                            initial={{ scale: 0.95, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.95, opacity: 0 }}
                            className="bg-white rounded-lg shadow-xl w-full max-w-lg"
                        >
                            <div className="flex justify-between items-center p-6 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {modalType === 'view' ? 'Inventory Details' :
                                     modalType === 'edit' ? 'Edit Inventory' : 'Add Inventory Item'}
                                </h3>
                                <button
                                    onClick={closeModal}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>

                            <div className="p-6">
                                {modalType === 'view' && selectedItem ? (
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Material</label>
                                            <p className="text-gray-900">{getMaterialName(selectedItem.material_id)}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Current Stock</label>
                                            <p className="text-gray-900 font-semibold text-lg">{selectedItem.current_stock}</p>
                                        </div>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">Minimum Stock</label>
                                                <p className="text-gray-900">{selectedItem.minimum_stock}</p>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">Maximum Stock</label>
                                                <p className="text-gray-900">{selectedItem.maximum_stock}</p>
                                            </div>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Reorder Point</label>
                                            <p className="text-gray-900">{selectedItem.reorder_point || 'Not set'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Supplier</label>
                                            <p className="text-gray-900">{getSupplierName(selectedItem.supplier_id)}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Last Restocked</label>
                                            <p className="text-gray-900">
                                                {selectedItem.last_restocked ?
                                                    new Date(selectedItem.last_restocked).toLocaleDateString() :
                                                    'Never restocked'
                                                }
                                            </p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStockStatus(selectedItem).color}`}>
                                                {getStockStatus(selectedItem).status}
                                            </span>
                                        </div>
                                    </div>
                                ) : (
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        {errors.submit && (
                                            <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center">
                                                <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                                                <span className="text-red-700 text-sm">{errors.submit}</span>
                                            </div>
                                        )}

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Material *
                                            </label>
                                            <select
                                                value={formData.material_id}
                                                onChange={(e) => setFormData({...formData, material_id: e.target.value})}
                                                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent ${
                                                    errors.material_id ? 'border-red-300' : 'border-gray-300'
                                                }`}
                                            >
                                                <option value="">Select a material</option>
                                                {materials.map(material => (
                                                    <option key={material.material_id} value={material.material_id}>
                                                        {material.material_name} ({material.category})
                                                    </option>
                                                ))}
                                            </select>
                                            {errors.material_id && (
                                                <p className="text-red-500 text-xs mt-1">{errors.material_id}</p>
                                            )}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Current Stock *
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={formData.current_stock}
                                                onChange={(e) => setFormData({...formData, current_stock: e.target.value})}
                                                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent ${
                                                    errors.current_stock ? 'border-red-300' : 'border-gray-300'
                                                }`}
                                                placeholder="0"
                                            />
                                            {errors.current_stock && (
                                                <p className="text-red-500 text-xs mt-1">{errors.current_stock}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Minimum Stock *
                                                </label>
                                                <input
                                                    type="number"
                                                    step="0.01"
                                                    value={formData.minimum_stock}
                                                    onChange={(e) => setFormData({...formData, minimum_stock: e.target.value})}
                                                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent ${
                                                        errors.minimum_stock ? 'border-red-300' : 'border-gray-300'
                                                    }`}
                                                    placeholder="0"
                                                />
                                                {errors.minimum_stock && (
                                                    <p className="text-red-500 text-xs mt-1">{errors.minimum_stock}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Maximum Stock *
                                                </label>
                                                <input
                                                    type="number"
                                                    step="0.01"
                                                    value={formData.maximum_stock}
                                                    onChange={(e) => setFormData({...formData, maximum_stock: e.target.value})}
                                                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent ${
                                                        errors.maximum_stock ? 'border-red-300' : 'border-gray-300'
                                                    }`}
                                                    placeholder="0"
                                                />
                                                {errors.maximum_stock && (
                                                    <p className="text-red-500 text-xs mt-1">{errors.maximum_stock}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Reorder Point
                                                </label>
                                                <input
                                                    type="number"
                                                    step="0.01"
                                                    value={formData.reorder_point}
                                                    onChange={(e) => setFormData({...formData, reorder_point: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                                                    placeholder="0"
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Last Restocked
                                                </label>
                                                <input
                                                    type="date"
                                                    value={formData.last_restocked}
                                                    onChange={(e) => setFormData({...formData, last_restocked: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                                                />
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Supplier (Optional)
                                            </label>
                                            <select
                                                value={formData.supplier_id}
                                                onChange={(e) => setFormData({...formData, supplier_id: e.target.value})}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                                            >
                                                <option value="">Select supplier</option>
                                                {suppliers.map(supplier => (
                                                    <option key={supplier.supplier_id} value={supplier.supplier_id}>
                                                        {supplier.supplier_name}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        <div className="flex justify-end space-x-3 pt-4">
                                            <button
                                                type="button"
                                                onClick={closeModal}
                                                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={submitLoading}
                                                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 flex items-center space-x-2 transition-colors"
                                            >
                                                {submitLoading ? (
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                                ) : (
                                                    <Save className="w-4 h-4" />
                                                )}
                                                <span>{modalType === 'edit' ? 'Update' : 'Create'}</span>
                                            </button>
                                        </div>
                                    </form>
                                )}
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export default InventoryManagement;
