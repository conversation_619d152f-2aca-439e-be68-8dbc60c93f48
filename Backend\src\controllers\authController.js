import AuthService from '../services/authService.js';

/**
 * Authentication Controller
 * Handles user authentication and management
 */
class AuthController {
    /**
     * Login user
     */
    static async login(req, res) {
        try {
            console.log('🔐 Login attempt received:', req.body);
            const { username, password } = req.body;

            if (!username || !password) {
                console.log('❌ Missing credentials');
                return res.status(400).json({
                    success: false,
                    message: 'Username and password are required'
                });
            }

            const user = await AuthService.authenticate(username, password);
            
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid username or password'
                });
            }

            // Check if there's a database connection error
            if (user.error) {
                return res.status(503).json({
                    success: false,
                    message: user.error,
                    details: 'Database connection failed for your role'
                });
            }

            console.log(`✅ Login successful for ${username} with role ${user.role}`);
            
            res.json({
                success: true,
                message: 'Login successful',
                data: {
                    username: user.username,
                    role: user.role,
                    name: user.name,
                    token: user.token,
                    dbRole: user.dbRole
                }
            });
        } catch (error) {
            console.error('❌ Login error:', error);
            res.status(500).json({
                success: false,
                message: 'Login failed',
                error: error.message
            });
        }
    }

    /**
     * Logout user
     */
    static async logout(req, res) {
        try {
            const authHeader = req.headers['authorization'];
            const token = authHeader && authHeader.split(' ')[1];

            if (token) {
                AuthService.logout(token);
            }

            res.json({
                success: true,
                message: 'Logout successful'
            });
        } catch (error) {
            console.error('Logout error:', error);
            res.status(500).json({
                success: false,
                message: 'Logout failed',
                error: error.message
            });
        }
    }

    /**
     * Get current user info
     */
    static async getCurrentUser(req, res) {
        try {
            res.json({
                success: true,
                message: 'User info retrieved',
                data: {
                    username: req.user.username,
                    role: req.user.role,
                    name: req.user.name
                }
            });
        } catch (error) {
            console.error('Get current user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve user info',
                error: error.message
            });
        }
    }

    /**
     * Get all users (admin only)
     */
    static async getAllUsers(req, res) {
        try {
            const users = AuthService.getUsers();

            res.json({
                success: true,
                message: 'Users retrieved successfully',
                data: users
            });
        } catch (error) {
            console.error('Get all users error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve users',
                error: error.message
            });
        }
    }

    /**
     * Get available roles
     */
    static async getAvailableRoles(req, res) {
        try {
            const roles = AuthService.getAvailableRoles();

            res.json({
                success: true,
                message: 'Available roles retrieved successfully',
                data: roles
            });
        } catch (error) {
            console.error('Get available roles error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve available roles',
                error: error.message
            });
        }
    }

    /**
     * Create new user (admin only)
     */
    static async createUser(req, res) {
        try {
            const { username, password, role, name } = req.body;
            const requestingUser = req.user?.username; // Assuming middleware sets this

            console.log(`🔐 Creating user: ${username} with role: ${role} (requested by: ${requestingUser})`);

            const result = await AuthService.createUser({
                username,
                password,
                role,
                name
            }, requestingUser);

            if (result.success) {
                console.log(`✅ User creation successful: ${username}`);
                res.status(201).json({
                    success: true,
                    message: `User ${username} created successfully in both .env file and database`,
                    data: {
                        username,
                        role,
                        name,
                        envUser: result.envUser,
                        dbUser: result.dbUser,
                        dbUsername: result.dbUsername,
                        dbRole: result.dbRole
                    }
                });
            } else {
                console.log(`❌ User creation failed: ${result.message}`);
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('❌ Create user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create user',
                error: error.message
            });
        }
    }

    /**
     * Update user (admin only)
     */
    static async updateUser(req, res) {
        try {
            const { username } = req.params;
            const { password, role, name } = req.body;
            const requestingUser = req.user?.username; // Assuming middleware sets this

            console.log(`🔐 Updating user: ${username} (requested by: ${requestingUser})`);

            const result = await AuthService.updateUser(username, {
                password,
                role,
                name
            }, requestingUser);

            if (result.success) {
                console.log(`✅ User update successful: ${username}`);
                res.json({
                    success: true,
                    message: `User ${username} updated successfully in both .env file and database`,
                    data: {
                        username,
                        envUpdate: result.envUpdate,
                        dbUpdates: result.dbUpdates
                    }
                });
            } else {
                console.log(`❌ User update failed: ${result.message}`);
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('❌ Update user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update user',
                error: error.message
            });
        }
    }

    /**
     * Delete user (admin only)
     */
    static async deleteUser(req, res) {
        try {
            const { username } = req.params;
            const requestingUser = req.user?.username; // Assuming middleware sets this

            console.log(`🔐 Deleting user: ${username} (requested by: ${requestingUser})`);

            const result = await AuthService.deleteUser(username, requestingUser);

            if (result.success) {
                console.log(`✅ User deletion successful: ${username}`);
                res.json({
                    success: true,
                    message: `User ${username} deleted successfully from both .env file and database`,
                    data: {
                        username,
                        envDelete: result.envDelete,
                        dbDelete: result.dbDelete,
                        dbMessage: result.dbMessage
                    }
                });
            } else {
                console.log(`❌ User deletion failed: ${result.message}`);
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('❌ Delete user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete user',
                error: error.message
            });
        }
    }
}

export default AuthController;
