import React, { useState } from 'react';
import { api } from '../../services/api.js';
import DataTable from '../common/DataTable';

const EmployeeManagement = () => {
    const [selectedEmployee, setSelectedEmployee] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create

    const columns = [
        {
            key: 'employee_id',
            label: 'ID',
            sortable: true
        },
        {
            key: 'employee_name',
            label: 'Name',
            sortable: true
        },
        {
            key: 'email',
            label: 'Email',
            sortable: true
        },
        {
            key: 'phone',
            label: 'Phone',
            sortable: true
        },
        {
            key: 'role',
            label: 'Role',
            sortable: true,
            render: (value) => (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    value === 'admin' ? 'bg-red-100 text-red-800' :
                    value === 'manager' ? 'bg-blue-100 text-blue-800' :
                    value === 'cashier' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                }`}>
                    {value}
                </span>
            )
        },
        {
            key: 'position',
            label: 'Position',
            sortable: true
        },
        {
            key: 'salary',
            label: 'Salary',
            sortable: true,
            render: (value) => value ? `$${parseFloat(value).toLocaleString()}` : 'N/A'
        },
        {
            key: 'hire_date',
            label: 'Hire Date',
            sortable: true,
            render: (value) => new Date(value).toLocaleDateString()
        }
    ];

    const actions = [
        {
            label: 'View',
            onClick: (employee) => {
                setSelectedEmployee(employee);
                setModalType('view');
                setShowModal(true);
            },
            className: 'text-blue-600 hover:text-blue-900'
        },
        {
            label: 'Edit',
            onClick: (employee) => {
                setSelectedEmployee(employee);
                setModalType('edit');
                setShowModal(true);
            },
            className: 'text-green-600 hover:text-green-900'
        },
        {
            label: 'Delete',
            onClick: (employee) => {
                if (window.confirm(`Are you sure you want to delete employee ${employee.employee_name}?`)) {
                    handleDeleteEmployee(employee.employee_id);
                }
            },
            className: 'text-red-600 hover:text-red-900'
        }
    ];

    const handleDeleteEmployee = async (employeeId) => {
        try {
            await api.employees.delete(employeeId);
            // Refresh the table data
            window.location.reload();
        } catch (error) {
            console.error('Error deleting employee:', error);
            alert('Failed to delete employee');
        }
    };

    const fetchEmployees = async (params) => {
        try {
            const response = await api.employees.getAll(params);
            return response;
        } catch (error) {
            console.error('Error fetching employees:', error);
            throw error;
        }
    };

    const EmployeeModal = () => {
        if (!showModal) return null;

        return (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div className="mt-3">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">
                                {modalType === 'view' ? 'Employee Details' : 
                                 modalType === 'edit' ? 'Edit Employee' : 'Create Employee'}
                            </h3>
                            <button
                                onClick={() => setShowModal(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        {selectedEmployee && (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Name</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedEmployee.employee_name}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Email</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedEmployee.email}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedEmployee.phone}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Role</label>
                                    <div className="mt-1">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                            selectedEmployee.role === 'admin' ? 'bg-red-100 text-red-800' :
                                            selectedEmployee.role === 'manager' ? 'bg-blue-100 text-blue-800' :
                                            selectedEmployee.role === 'cashier' ? 'bg-green-100 text-green-800' :
                                            'bg-gray-100 text-gray-800'
                                        }`}>
                                            {selectedEmployee.role}
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Position</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedEmployee.position}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Salary</label>
                                    <div className="mt-1 text-sm text-gray-900">
                                        {selectedEmployee.salary ? `$${parseFloat(selectedEmployee.salary).toLocaleString()}` : 'N/A'}
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Hire Date</label>
                                    <div className="mt-1 text-sm text-gray-900">
                                        {new Date(selectedEmployee.hire_date).toLocaleDateString()}
                                    </div>
                                </div>
                            </div>
                        )}
                        
                        <div className="mt-6 flex justify-end space-x-3">
                            <button
                                onClick={() => setShowModal(false)}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">Employee Management</h2>
                <button
                    onClick={() => {
                        setSelectedEmployee(null);
                        setModalType('create');
                        setShowModal(true);
                    }}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                >
                    Add Employee
                </button>
            </div>

            <DataTable
                title="Employees"
                columns={columns}
                fetchData={fetchEmployees}
                actions={actions}
                searchPlaceholder="Search employees..."
                defaultSort={{ field: 'employee_id', order: 'desc' }}
            />

            <EmployeeModal />
        </div>
    );
};

export default EmployeeManagement;
