import React, { useState, useEffect } from 'react';
import { api } from '../../services/api.js';

const MaterialAnalytics = () => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [materialData, setMaterialData] = useState({
        materialCostsPerMonth: [],
        stockLevels: [],
        supplierSpending: [],
        rawMaterialCosts: []
    });

    useEffect(() => {
        fetchMaterialData();
    }, []);

    const fetchMaterialData = async () => {
        try {
            setLoading(true);
            const [
                materialCostsRes,
                stockLevelsRes,
                supplierSpendingRes,
                rawMaterialCostsRes
            ] = await Promise.all([
                api.get('/analytics/material-costs'),
                api.get('/analytics/materials/stock-levels'),
                api.get('/analytics/suppliers/spending'),
                api.get('/analytics/products/raw-material-cost')
            ]);

            setMaterialData({
                materialCostsPerMonth: materialCostsRes.data.data || [],
                stockLevels: stockLevelsRes.data.data || [],
                supplierSpending: supplierSpendingRes.data.data || [],
                rawMaterialCosts: rawMaterialCostsRes.data.data || []
            });
        } catch (error) {
            console.error('Error fetching material data:', error);
            setError('Failed to load material analytics');
        } finally {
            setLoading(false);
        }
    };

    const StatCard = ({ title, value, subtitle, icon, color = 'blue' }) => (
        <div className={`bg-white rounded-lg shadow-md p-6 border-l-4 border-${color}-500`}>
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-2xl font-bold text-gray-900">{value}</p>
                    {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
                </div>
                <div className="text-3xl">{icon}</div>
            </div>
        </div>
    );

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                    <div className="text-red-500">⚠️</div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="text-sm text-red-700">{error}</div>
                    </div>
                </div>
            </div>
        );
    }

    const totalMaterials = materialData.stockLevels.length;
    const lowStockMaterials = materialData.stockLevels.filter(material => material.quantity < 10);
    const outOfStockMaterials = materialData.stockLevels.filter(material => material.quantity === 0);
    const totalSupplierSpending = materialData.supplierSpending.reduce((sum, supplier) => sum + parseFloat(supplier.total_spent), 0);

    return (
        <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                    title="Total Materials"
                    value={totalMaterials}
                    subtitle="In inventory"
                    icon="📦"
                    color="blue"
                />
                <StatCard
                    title="Low Stock Items"
                    value={lowStockMaterials.length}
                    subtitle="Below 10 units"
                    icon="⚠️"
                    color="orange"
                />
                <StatCard
                    title="Out of Stock"
                    value={outOfStockMaterials.length}
                    subtitle="Need reorder"
                    icon="🚨"
                    color="red"
                />
                <StatCard
                    title="Supplier Spending"
                    value={`$${totalSupplierSpending.toLocaleString()}`}
                    subtitle="Total spent"
                    icon="💰"
                    color="green"
                />
            </div>

            {/* Material Stock Levels */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Material Stock Levels</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Eating Materials */}
                    <div>
                        <h4 className="font-medium text-gray-700 mb-3">Eating Materials</h4>
                        <div className="space-y-2">
                            {materialData.stockLevels
                                .filter(material => material.type === 'eating')
                                .map((material, index) => {
                                    const stockLevel = material.quantity;
                                    const stockColor = stockLevel === 0 ? 'red' : stockLevel < 10 ? 'orange' : 'green';
                                    
                                    return (
                                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div className="flex items-center">
                                                <div className={`w-3 h-3 rounded-full bg-${stockColor}-500 mr-3`}></div>
                                                <span className="text-sm font-medium text-gray-900">
                                                    {material.name_material}
                                                </span>
                                            </div>
                                            <span className={`text-sm font-bold ${stockColor === 'red' ? 'text-red-600' : stockColor === 'orange' ? 'text-orange-600' : 'text-green-600'}`}>
                                                {stockLevel} units
                                            </span>
                                        </div>
                                    );
                                })}
                        </div>
                    </div>

                    {/* Object Materials */}
                    <div>
                        <h4 className="font-medium text-gray-700 mb-3">Object Materials</h4>
                        <div className="space-y-2">
                            {materialData.stockLevels
                                .filter(material => material.type === 'object')
                                .map((material, index) => {
                                    const stockLevel = material.quantity;
                                    const stockColor = stockLevel === 0 ? 'red' : stockLevel < 10 ? 'orange' : 'green';
                                    
                                    return (
                                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                            <div className="flex items-center">
                                                <div className={`w-3 h-3 rounded-full bg-${stockColor}-500 mr-3`}></div>
                                                <span className="text-sm font-medium text-gray-900">
                                                    {material.name_material}
                                                </span>
                                            </div>
                                            <span className={`text-sm font-bold ${stockColor === 'red' ? 'text-red-600' : stockColor === 'orange' ? 'text-orange-600' : 'text-green-600'}`}>
                                                {stockLevel} units
                                            </span>
                                        </div>
                                    );
                                })}
                        </div>
                    </div>
                </div>
            </div>

            {/* Material Costs per Month */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Material Costs per Month</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Month
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Material Type
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Cost
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {materialData.materialCostsPerMonth.map((cost, index) => (
                                <tr key={index} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {cost.month}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                            cost.material_type === 'eating' 
                                                ? 'bg-green-100 text-green-800' 
                                                : 'bg-blue-100 text-blue-800'
                                        }`}>
                                            {cost.material_type}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${parseFloat(cost.total_cost).toLocaleString()}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Supplier Spending */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Supplier Spending Analysis</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Supplier
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Category
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Spent
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    % of Total
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {materialData.supplierSpending.map((supplier, index) => {
                                const percentage = totalSupplierSpending > 0 ? ((parseFloat(supplier.total_spent) / totalSupplierSpending) * 100).toFixed(1) : 0;
                                
                                return (
                                    <tr key={index} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {supplier.supplies_name}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                                                {supplier.category}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${parseFloat(supplier.total_spent).toLocaleString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {percentage}%
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Raw Material Costs per Product */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Raw Material Costs per Product</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {materialData.rawMaterialCosts.slice(0, 12).map((product, index) => {
                        const cost = parseFloat(product.raw_material_cost);
                        const costLevel = cost > 20 ? 'High' : cost > 10 ? 'Medium' : 'Low';
                        const costColor = cost > 20 ? 'red' : cost > 10 ? 'yellow' : 'green';
                        
                        return (
                            <div key={index} className="bg-gray-50 rounded-lg p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex-1">
                                        <div className="font-medium text-gray-900 truncate">
                                            {product.product_name}
                                        </div>
                                        <div className="text-sm text-gray-600">
                                            Raw Material Cost
                                        </div>
                                    </div>
                                    <div className="text-right ml-3">
                                        <div className="text-lg font-bold text-gray-900">
                                            ${cost.toFixed(2)}
                                        </div>
                                        <span className={`px-2 py-1 text-xs font-medium bg-${costColor}-100 text-${costColor}-800 rounded-full`}>
                                            {costLevel}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default MaterialAnalytics;
