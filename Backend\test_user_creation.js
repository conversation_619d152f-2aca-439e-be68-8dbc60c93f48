import dotenv from 'dotenv';
import DatabaseUserManager from './src/utils/databaseUserManager.js';

// Load environment variables
dotenv.config();

async function testDatabaseUserCreation() {
    console.log('🧪 Testing Database User Creation for mengheng...\n');
    
    try {
        // Test creating database user for mengheng
        const result = await DatabaseUserManager.createDatabaseUser({
            username: 'mengheng',
            password: '123456',
            role: 'manager'
        });
        
        console.log('📊 Creation Result:', result);
        
        if (result.success) {
            console.log('\n🔍 Testing database user connection...');
            
            // Test the database user connection
            const testResult = await DatabaseUserManager.testDatabaseUser('mengheng', '123456');
            console.log('📊 Connection Test Result:', testResult);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testDatabaseUserCreation();
