import mysql from 'mysql2/promise';
import fs from 'fs';
import dotenv from "dotenv";
dotenv.config();

// Connection pools for different roles
const connectionPools = new Map();

/**
 * Get database credentials based on user role
 * @param {string} role - User role (admin, manager, cashier, vendor, barista, user)
 * @param {string} username - Specific username (optional)
 * @returns {Object} Database credentials
 */
function getDatabaseCredentials(role, username = null) {
    const credentials = {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME,
    };

    // If username is provided, try to use the specific database user
    if (username) {
        credentials.user = `app_${username}`;
        credentials.password = `${getUserPassword(username)}_db`;
        return credentials;
    }

    switch (role) {
        case 'admin':
            credentials.user = process.env.DB_USER || 'avnadmin';
            credentials.password = process.env.DB_PASSWORD || 'AVNS_KiWXNwxtH4tpuvcCgbt';
            break;
        case 'manager':
            credentials.user = process.env.DB_USER || 'avnadmin';
            credentials.password = process.env.DB_PASSWORD || 'AVNS_KiWXNwxtH4tpuvcCgbt';
            break;
        case 'cashier':
            credentials.user = process.env.DB_USER || 'avnadmin';
            credentials.password = process.env.DB_PASSWORD || 'AVNS_KiWXNwxtH4tpuvcCgbt';
            break;
        case 'vendor':
            credentials.user = process.env.DB_USER || 'avnadmin';
            credentials.password = process.env.DB_PASSWORD || 'AVNS_KiWXNwxtH4tpuvcCgbt';
            break;
        case 'barista':
            credentials.user = process.env.DB_USER || 'avnadmin';
            credentials.password = process.env.DB_PASSWORD || 'AVNS_KiWXNwxtH4tpuvcCgbt';
            break;
        case 'user':
            credentials.user = process.env.DB_USER || 'avnadmin';
            credentials.password = process.env.DB_PASSWORD || 'AVNS_KiWXNwxtH4tpuvcCgbt';
            break;
        default:
            // Fallback to admin credentials
            credentials.user = process.env.DB_USER || 'avnadmin';
            credentials.password = process.env.DB_PASSWORD || 'AVNS_KiWXNwxtH4tpuvcCgbt';
    }

    return credentials;
}

/**
 * Get user password from environment file
 * @param {string} username - Username
 * @returns {string} User password
 */
function getUserPassword(username) {
    // Parse the .env file to get the user's password
    const envContent = process.env;
    
    // Look for the user in the environment variables
    for (let i = 1; i <= 100; i++) {
        const userEntry = process.env[`USER_AND_PASSWORD_AND_HOST_${i}`];
        if (userEntry) {
            const [envUsername, password] = userEntry.split(',');
            if (envUsername === username) {
                return password;
            }
        }
    }
    
    // Default password if not found
    return '123456';
}

/**
 * Get CA certificate for SSL connection
 * @returns {Buffer} CA certificate
 */
function getCACertificate() {
    let caPath = 'ca.pem';
    let ca;
    try {
        ca = fs.readFileSync(new URL(caPath, import.meta.url));
    } catch (e) {
        try {
            ca = fs.readFileSync(new URL('../../ca.pem', import.meta.url));
        } catch (e2) {
            throw new Error('ca.pem not found in ./config or project root.');
        }
    }
    return ca;
}

/**
 * Create or get connection pool for specific role
 * @param {string} role - User role
 * @returns {Object} MySQL connection pool
 */
function getRoleBasedPool(role) {
    if (connectionPools.has(role)) {
        return connectionPools.get(role);
    }

    const credentials = getDatabaseCredentials(role);
    const ca = getCACertificate();

    const pool = mysql.createPool({
        ...credentials,
        ssl: {
            ca: ca
        },
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
        acquireTimeout: 60000,
        timeout: 60000,
        reconnect: true
    });

    connectionPools.set(role, pool);
    
    console.log(`\n📊 Database Pool Created for Role: ${role}`);
    console.log(`   User: ${credentials.user}`);
    console.log(`   Host: ${credentials.host}:${credentials.port}`);
    console.log(`   Database: ${credentials.database}`);

    return pool;
}

/**
 * Get database connection for specific user role
 * @param {string} userRole - User role (admin, manager, cashier, etc.)
 * @param {string} username - Specific username (optional)
 * @returns {Promise<Connection>} Database connection
 */
export async function connectToAivenDB(userRole = 'admin', username = null) {
    try {
        const poolKey = username ? `${userRole}_${username}` : userRole;
        
        if (connectionPools.has(poolKey)) {
            const pool = connectionPools.get(poolKey);
            const connection = await pool.getConnection();
            console.log(`\n🔗 Database Connection Established for ${username ? `${username} (${userRole})` : userRole}`);
            return connection;
        }

        const credentials = getDatabaseCredentials(userRole, username);
        const ca = getCACertificate();

        const pool = mysql.createPool({
            ...credentials,
            ssl: {
                ca: ca
            },
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            acquireTimeout: 60000,
            timeout: 60000,
            reconnect: true
        });

        connectionPools.set(poolKey, pool);
        
        console.log(`\n📊 Database Pool Created for ${username ? `${username} (${userRole})` : userRole}`);
        console.log(`   User: ${credentials.user}`);
        console.log(`   Host: ${credentials.host}:${credentials.port}`);
        console.log(`   Database: ${credentials.database}`);

        const connection = await pool.getConnection();
        console.log(`\n🔗 Database Connection Established for ${username ? `${username} (${userRole})` : userRole}`);
        return connection;
    } catch (error) {
        console.error(`❌ Database connection failed for ${username ? `${username} (${userRole})` : userRole}:`, error.message);
        throw error;
    }
}

/**
 * Get the default admin connection (for system operations)
 * @returns {Promise<Connection>} Admin database connection
 */
export async function getAdminConnection() {
    return await connectToAivenDB('admin');
}

/**
 * Test database connection for a specific role
 * @param {string} role - User role
 * @param {string} username - Specific username (optional)
 * @returns {Promise<boolean>} Connection success status
 */
export async function testRoleConnection(role, username = null) {
    try {
        const connection = await connectToAivenDB(role, username);
        const [rows] = await connection.execute('SELECT 1 + 1 AS solution');
        console.log(`✅ ${username ? `${username} (${role})` : `Role ${role}`} connection test successful:`, rows);
        connection.release();
        return true;
    } catch (error) {
        console.error(`❌ ${username ? `${username} (${role})` : `Role ${role}`} connection test failed:`, error.message);
        return false;
    }
}

/**
 * Close all connection pools
 */
export async function closeAllPools() {
    for (const [role, pool] of connectionPools) {
        try {
            await pool.end();
            console.log(`✅ Connection pool for role ${role} closed`);
        } catch (error) {
            console.error(`❌ Error closing pool for role ${role}:`, error.message);
        }
    }
    connectionPools.clear();
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down database connections...');
    await closeAllPools();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Shutting down database connections...');
    await closeAllPools();
    process.exit(0);
});

export default {
    connectToAivenDB,
    getAdminConnection,
    testRoleConnection,
    closeAllPools
};
