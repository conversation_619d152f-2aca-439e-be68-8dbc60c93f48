// API Service for Coffee Management System
// This service provides functions to call all backend endpoints

const API_BASE_URL = 'http://localhost:3000/api';

// Helper function to handle API calls with authentication
const apiCall = async (endpoint, options = {}) => {
    const token = localStorage.getItem('token');

    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...(token && { Authorization: `Bearer ${token}` }),
        },
        ...options,
    };

    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error(`API call failed for ${endpoint}:`, error);
        throw error;
    }
};

// Helper function to build query parameters
const buildQueryParams = (params = {}) => {
    const urlParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            urlParams.append(key, value.toString());
        }
    });
    return urlParams.toString();
};

// Analytics API Functions
export const analyticsApi = {
    // Loyalty Points Analytics
    getLoyaltyPointsPerCustomer: () => apiCall('/analytics/loyalty-points'),
    
    // Material Analytics
    getMaterialCostsPerMonth: () => apiCall('/analytics/material-costs'),
    getMaterialStockLevels: () => apiCall('/analytics/materials/stock-levels'),
    
    // Product Analytics
    getProductsWithZeroQuantityMaterials: () => apiCall('/analytics/zero-quantity-products'),
    getMostSoldProduct: () => apiCall('/analytics/products/most-sold'),
    getRevenueByProduct: () => apiCall('/analytics/revenue/by-product'),
    getUnusedProducts: () => apiCall('/analytics/products/unused'),
    getMostFrequentlyOrderedProduct: () => apiCall('/analytics/products/most-frequent'),
    getRawMaterialCostPerProduct: () => apiCall('/analytics/products/raw-material-cost'),
    
    // Sales Analytics
    getSalesLastDays: () => apiCall('/analytics/sales/recent'),
    getSalesByMonth: () => apiCall('/analytics/sales/monthly'),
    
    // Customer Analytics
    getCustomerOrderHistory: () => apiCall('/analytics/customers/order-history'),
    getTopCustomersBySpending: () => apiCall('/analytics/customers/top-spenders'),
    
    // Supplier Analytics
    getSupplierSpending: () => apiCall('/analytics/suppliers/spending'),
    
    // Order Analytics
    getMonthlyOrderStats: () => apiCall('/analytics/orders/monthly-stats'),
    getOrdersWithNoPayment: () => apiCall('/analytics/orders/no-payment'),
};

// Auth API Functions
export const authApi = {
    login: (credentials) => apiCall('/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
    }),
    
    register: (userData) => apiCall('/auth/register', {
        method: 'POST',
        body: JSON.stringify(userData),
    }),
    
    logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
    },
    
    getCurrentUser: () => {
        const user = localStorage.getItem('user');
        return user ? JSON.parse(user) : null;
    },
    
    getToken: () => localStorage.getItem('token'),
    
    isAuthenticated: () => !!localStorage.getItem('token'),
};

// Customer API Functions with pagination
export const customerApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            sortBy: params.sortBy,
            sortOrder: params.sortOrder,
            ...params
        });
        return apiCall(`/customers?${queryParams}`);
    },
    getById: (id) => apiCall(`/customers/${id}`),
    create: (customerData) => apiCall('/customers', {
        method: 'POST',
        body: JSON.stringify(customerData),
    }),
    update: (id, customerData) => apiCall(`/customers/${id}`, {
        method: 'PUT',
        body: JSON.stringify(customerData),
    }),
    delete: (id) => apiCall(`/customers/${id}`, {
        method: 'DELETE',
    }),
};

// Employee API Functions with pagination
export const employeeApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            sortBy: params.sortBy,
            sortOrder: params.sortOrder,
            role: params.role,
            ...params
        });
        return apiCall(`/employees?${queryParams}`);
    },
    getById: (id) => apiCall(`/employees/${id}`),
    create: (employeeData) => apiCall('/employees', {
        method: 'POST',
        body: JSON.stringify(employeeData),
    }),
    update: (id, employeeData) => apiCall(`/employees/${id}`, {
        method: 'PUT',
        body: JSON.stringify(employeeData),
    }),
    delete: (id) => apiCall(`/employees/${id}`, {
        method: 'DELETE',
    }),
};

// Product API Functions with pagination
export const productApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            sortBy: params.sortBy,
            sortOrder: params.sortOrder,
            category: params.category,
            minPrice: params.minPrice,
            maxPrice: params.maxPrice,
            ...params
        });
        return apiCall(`/products?${queryParams}`);
    },
    getById: (id) => apiCall(`/products/${id}`),
    create: (productData) => apiCall('/products', {
        method: 'POST',
        body: JSON.stringify(productData),
    }),
    update: (id, productData) => apiCall(`/products/${id}`, {
        method: 'PUT',
        body: JSON.stringify(productData),
    }),
    delete: (id) => apiCall(`/products/${id}`, {
        method: 'DELETE',
    }),
};

// Order API Functions with pagination
export const orderApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            sortBy: params.sortBy,
            sortOrder: params.sortOrder,
            status: params.status,
            customerId: params.customerId,
            dateFrom: params.dateFrom,
            dateTo: params.dateTo,
            ...params
        });
        return apiCall(`/orders?${queryParams}`);
    },
    getById: (id) => apiCall(`/orders/${id}`),
    create: (orderData) => apiCall('/orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
    }),
    update: (id, orderData) => apiCall(`/orders/${id}`, {
        method: 'PUT',
        body: JSON.stringify(orderData),
    }),
    delete: (id) => apiCall(`/orders/${id}`, {
        method: 'DELETE',
    }),
};

// Material API Functions with pagination
export const materialApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            sortBy: params.sortBy,
            sortOrder: params.sortOrder,
            type: params.type,
            lowStock: params.lowStock,
            ...params
        });
        return apiCall(`/materials?${queryParams}`);
    },
    getById: (id) => apiCall(`/materials/${id}`),
    create: (materialData) => apiCall('/materials', {
        method: 'POST',
        body: JSON.stringify(materialData),
    }),
    update: (id, materialData) => apiCall(`/materials/${id}`, {
        method: 'PUT',
        body: JSON.stringify(materialData),
    }),
    delete: (id) => apiCall(`/materials/${id}`, {
        method: 'DELETE',
    }),
};

// Supplier API Functions with pagination
export const supplierApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            sortBy: params.sortBy,
            sortOrder: params.sortOrder,
            ...params
        });
        return apiCall(`/suppliers?${queryParams}`);
    },
    getById: (id) => apiCall(`/suppliers/${id}`),
    create: (supplierData) => apiCall('/suppliers', {
        method: 'POST',
        body: JSON.stringify(supplierData),
    }),
    update: (id, supplierData) => apiCall(`/suppliers/${id}`, {
        method: 'PUT',
        body: JSON.stringify(supplierData),
    }),
    delete: (id) => apiCall(`/suppliers/${id}`, {
        method: 'DELETE',
    }),
};

// Payment API Functions with pagination
export const paymentApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            sortBy: params.sortBy,
            sortOrder: params.sortOrder,
            status: params.status,
            method: params.method,
            dateFrom: params.dateFrom,
            dateTo: params.dateTo,
            ...params
        });
        return apiCall(`/payments?${queryParams}`);
    },
    getById: (id) => apiCall(`/payments/${id}`),
    create: (paymentData) => apiCall('/payments', {
        method: 'POST',
        body: JSON.stringify(paymentData),
    }),
    update: (id, paymentData) => apiCall(`/payments/${id}`, {
        method: 'PUT',
        body: JSON.stringify(paymentData),
    }),
    delete: (id) => apiCall(`/payments/${id}`, {
        method: 'DELETE',
    }),
};

// Material API Functions with pagination
export const materialApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            type: params.type,
            category: params.category,
            ...params
        });
        return apiCall(`/materials?${queryParams}`);
    },
    getById: (id) => apiCall(`/materials/${id}`),
    create: (materialData) => apiCall('/materials', {
        method: 'POST',
        body: JSON.stringify(materialData),
    }),
    update: (id, materialData) => apiCall(`/materials/${id}`, {
        method: 'PUT',
        body: JSON.stringify(materialData),
    }),
    delete: (id) => apiCall(`/materials/${id}`, {
        method: 'DELETE',
    }),
};

// Inventory API Functions with pagination
export const inventoryApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 500,
            search: params.search,
            status: params.status,
            ...params
        });
        return apiCall(`/inventory?${queryParams}`);
    },
    getById: (id) => apiCall(`/inventory/${id}`),
    create: (inventoryData) => apiCall('/inventory', {
        method: 'POST',
        body: JSON.stringify(inventoryData),
    }),
    update: (id, inventoryData) => apiCall(`/inventory/${id}`, {
        method: 'PUT',
        body: JSON.stringify(inventoryData),
    }),
    delete: (id) => apiCall(`/inventory/${id}`, {
        method: 'DELETE',
    }),
};

// Analytics API Functions
export const analyticsApi = {
    getDashboard: (params = {}) => {
        const queryParams = buildQueryParams({
            timeRange: params.timeRange || '7d',
            ...params
        });
        return apiCall(`/analytics/dashboard?${queryParams}`);
    },
    getRevenue: (params = {}) => {
        const queryParams = buildQueryParams(params);
        return apiCall(`/analytics/revenue?${queryParams}`);
    },
    getTopProducts: (params = {}) => {
        const queryParams = buildQueryParams(params);
        return apiCall(`/analytics/top-products?${queryParams}`);
    },
    getCustomerStats: (params = {}) => {
        const queryParams = buildQueryParams(params);
        return apiCall(`/analytics/customers?${queryParams}`);
    },
};

// Activity API Functions
export const activityApi = {
    getAll: (params = {}) => {
        const queryParams = buildQueryParams({
            page: params.page || 1,
            limit: params.limit || 20,
            search: params.search,
            type: params.type,
            user: params.user,
            dateFrom: params.dateFrom,
            dateTo: params.dateTo,
            ...params
        });
        return apiCall(`/activity?${queryParams}`);
    },
    getById: (id) => apiCall(`/activity/${id}`),
    create: (activityData) => apiCall('/activity', {
        method: 'POST',
        body: JSON.stringify(activityData),
    }),
};

// Main API object for easy access
export const coffeeAPI = {
    // Analytics methods
    getAnalytics: () => apiCall('/analytics'),

    // Generic API methods for direct usage
    get: (endpoint) => apiCall(endpoint),
    post: (endpoint, data) => apiCall(endpoint, {
        method: 'POST',
        body: JSON.stringify(data),
    }),
    put: (endpoint, data) => apiCall(endpoint, {
        method: 'PUT',
        body: JSON.stringify(data),
    }),
    delete: (endpoint) => apiCall(endpoint, {
        method: 'DELETE',
    }),

    // Individual API collections
    analytics: analyticsApi,
    auth: authApi,
    customers: customerApi,
    employees: employeeApi,
    products: productApi,
    orders: orderApi,
    materials: materialApi,
    suppliers: supplierApi,
    payments: paymentApi,
    inventory: inventoryApi,
    activity: activityApi,
};

// Named export for direct usage
export const api = coffeeAPI;

// Default export for backward compatibility
export default {
    analyticsApi,
    authApi,
    customerApi,
    employeeApi,
    productApi,
    orderApi,
    materialApi,
    supplierApi,
    paymentApi,
    inventoryApi,
    activityApi,
    coffeeAPI,
};
