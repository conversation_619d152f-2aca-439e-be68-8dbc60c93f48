import os
import subprocess

# Database credentials
host = "coffe-management-db1st-choengrayu307-607c.b.aivencloud.com"
port = 21011
user = "avnadmin"
password = "AVNS_KiWXNwxtH4tpuvcCgbt"
database = "defaultdb"

# Backup file to restore
backup_file = "D:\Year2\Term3\DataBase-\ProjectDB-Year2-Term3\BackupRecovery\coffee_management_db_backup_date_2025-07-16_time_15-25-06.sql"  # Replace with the actual file path

# MySQL restore command
restore_command = f"mysql --host={host} --port={port} --user={user} --password={password} {database} < {backup_file}"

# Execute the restore command
try:
    subprocess.run(restore_command, shell=True, check=True)
    print(f"Database restored successfully from: {backup_file}")
except subprocess.CalledProcessError as e:
    print(f"Error occurred during recovery: {e}")