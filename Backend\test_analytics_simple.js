// Simple test script for analytics endpoints
const testEndpoints = async () => {
    console.log('🧪 Testing Analytics Endpoints...\n');
    
    const endpoints = [
        '/analytics/loyalty-points',
        '/analytics/material-costs',
        '/analytics/sales/recent?days=7',
        '/analytics/products/most-sold',
        '/analytics/customers/top-spenders?limit=5',
        '/analytics/sales/monthly',
        '/analytics/revenue/by-product',
        '/analytics/products/unused',
        '/analytics/materials/stock-levels',
        '/analytics/orders/no-payment',
        '/analytics/suppliers/spending',
        '/analytics/orders/monthly-stats'
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`Testing ${endpoint}...`);
            const response = await fetch(`http://localhost:3000/api${endpoint}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ ${endpoint}: SUCCESS - ${data.data ? data.data.length : 0} records`);
            } else {
                console.log(`❌ ${endpoint}: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            console.log(`❌ ${endpoint}: ${error.message}`);
        }
    }
    
    console.log('\n🎉 Testing completed!');
};

testEndpoints();
