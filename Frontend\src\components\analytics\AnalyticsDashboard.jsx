import React, { useState, useEffect } from 'react';
import { api } from '../../services/api.js';
import LoyaltyPointsChart from './LoyaltyPointsChart';
import SalesChart from './SalesChart';
import ProductAnalytics from './ProductAnalytics';
import CustomerAnalytics from './CustomerAnalytics';
import MaterialAnalytics from './MaterialAnalytics';
import OrderAnalytics from './OrderAnalytics';

const AnalyticsDashboard = () => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [activeTab, setActiveTab] = useState('overview');
    const [dashboardData, setDashboardData] = useState({
        recentSales: [],
        topCustomers: [],
        mostSoldProduct: null,
        mostFrequentProduct: null,
        monthlyStats: [],
        unpaidOrders: [],
        unusedProducts: [],
        materialStockLevels: []
    });

    // Fetch dashboard overview data
    const fetchDashboardData = async () => {
        try {
            setLoading(true);
            const [
                recentSalesRes,
                topCustomersRes,
                mostSoldProductRes,
                mostFrequentProductRes,
                monthlyStatsRes,
                unpaidOrdersRes,
                unusedProductsRes,
                materialStockRes
            ] = await Promise.all([
                api.analytics.getSalesLastDays(),
                api.analytics.getTopCustomersBySpending(),
                api.analytics.getMostSoldProduct(),
                api.analytics.getMostFrequentlyOrderedProduct(),
                api.analytics.getMonthlyOrderStats(),
                api.analytics.getOrdersWithNoPayment(),
                api.analytics.getUnusedProducts(),
                api.analytics.getMaterialStockLevels()
            ]);

            setDashboardData({
                recentSales: recentSalesRes.data || [],
                topCustomers: topCustomersRes.data || [],
                mostSoldProduct: mostSoldProductRes.data,
                mostFrequentProduct: mostFrequentProductRes.data,
                monthlyStats: monthlyStatsRes.data || [],
                unpaidOrders: unpaidOrdersRes.data || [],
                unusedProducts: unusedProductsRes.data || [],
                materialStockLevels: materialStockRes.data || []
            });
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
            setError('Failed to load dashboard data');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const tabs = [
        { id: 'overview', label: 'Overview', icon: '📊' },
        { id: 'sales', label: 'Sales', icon: '💰' },
        { id: 'products', label: 'Products', icon: '📦' },
        { id: 'customers', label: 'Customers', icon: '👥' },
        { id: 'materials', label: 'Materials', icon: '🏭' },
        { id: 'orders', label: 'Orders', icon: '📋' }
    ];

    const StatCard = ({ title, value, subtitle, icon, color = 'blue' }) => (
        <div className={`bg-white rounded-lg shadow-md p-6 border-l-4 border-${color}-500`}>
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-2xl font-bold text-gray-900">{value}</p>
                    {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
                </div>
                <div className="text-3xl">{icon}</div>
            </div>
        </div>
    );

    const OverviewTab = () => {
        const totalRecentSales = dashboardData.recentSales.reduce((sum, sale) => sum + parseFloat(sale.total_sales || 0), 0);
        const totalRecentOrders = dashboardData.recentSales.reduce((sum, sale) => sum + parseInt(sale.order_count || 0), 0);
        const lowStockMaterials = dashboardData.materialStockLevels.filter(material => material.quantity < 10);
        
        return (
            <div className="space-y-6">
                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <StatCard
                        title="7-Day Sales"
                        value={`$${totalRecentSales.toLocaleString()}`}
                        subtitle={`${totalRecentOrders} orders`}
                        icon="💰"
                        color="green"
                    />
                    <StatCard
                        title="Top Product"
                        value={dashboardData.mostSoldProduct?.product_name || 'N/A'}
                        subtitle={`${dashboardData.mostSoldProduct?.total_quantity_sold || 0} sold`}
                        icon="🏆"
                        color="yellow"
                    />
                    <StatCard
                        title="Unpaid Orders"
                        value={dashboardData.unpaidOrders.length}
                        subtitle="Need attention"
                        icon="⚠️"
                        color="red"
                    />
                    <StatCard
                        title="Low Stock Items"
                        value={lowStockMaterials.length}
                        subtitle="Below 10 units"
                        icon="📦"
                        color="orange"
                    />
                </div>

                {/* Recent Sales Chart */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold mb-4">Recent Sales Trend</h3>
                    <SalesChart data={dashboardData.recentSales} />
                </div>

                {/* Top Customers */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold mb-4">Top Customers</h3>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Customer
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Email
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total Spent
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Orders
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {dashboardData.topCustomers.map((customer, index) => (
                                    <tr key={customer.customer_id}>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                                                    {index + 1}
                                                </div>
                                                <div className="ml-3">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {customer.customer_name}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {customer.email}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${parseFloat(customer.total_spent).toLocaleString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {customer.total_orders}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        );
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                    <div className="text-red-500">⚠️</div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="text-sm text-red-700">{error}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="bg-white shadow">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
                        <button
                            onClick={fetchDashboardData}
                            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            Refresh Data
                        </button>
                    </div>
                </div>
            </div>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Tab Navigation */}
                <div className="border-b border-gray-200 mb-8">
                    <nav className="-mb-px flex space-x-8">
                        {tabs.map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`py-2 px-4 border-b-2 font-medium text-sm transition-colors ${
                                    activeTab === tab.id
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                <span className="mr-2">{tab.icon}</span>
                                {tab.label}
                            </button>
                        ))}
                    </nav>
                </div>

                {/* Tab Content */}
                <div className="tab-content">
                    {activeTab === 'overview' && <OverviewTab />}
                    {activeTab === 'sales' && <SalesChart />}
                    {activeTab === 'products' && <ProductAnalytics />}
                    {activeTab === 'customers' && <CustomerAnalytics />}
                    {activeTab === 'materials' && <MaterialAnalytics />}
                    {activeTab === 'orders' && <OrderAnalytics />}
                </div>
            </div>
        </div>
    );
};

export default AnalyticsDashboard;
