import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
    ShoppingBag, Plus, Search, Edit, Trash2, Eye, X, 
    User, Package, Calendar, DollarSign, Save, AlertCircle, Clock 
} from 'lucide-react';
import { orderApi, customerApi, productApi } from '../../services/api.js';

const OrderManagement = () => {
    const [orders, setOrders] = useState([]);
    const [customers, setCustomers] = useState([]);
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [formData, setFormData] = useState({
        customer_id: '',
        product_id: '',
        quantity: 1,
        unit_price: '',
        status_order: 'Pending'
    });
    const [errors, setErrors] = useState({});
    const [submitLoading, setSubmitLoading] = useState(false);

    const orderStatuses = ['Pending', 'Completed'];

    useEffect(() => {
        fetchOrders();
        fetchCustomers();
        fetchProducts();
    }, [currentPage, searchTerm]);

    const fetchOrders = async () => {
        try {
            setLoading(true);
            const response = await orderApi.getAll({
                page: currentPage,
                limit: 10,
                search: searchTerm
            });
            
            if (response.success) {
                setOrders(response.data);
                setTotalPages(response.pagination?.totalPages || 1);
            }
        } catch (error) {
            console.error('Error fetching orders:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchCustomers = async () => {
        try {
            const response = await customerApi.getAll({ limit: 500 });
            if (response.success) {
                setCustomers(response.data);
            }
        } catch (error) {
            console.error('Error fetching customers:', error);
        }
    };

    const fetchProducts = async () => {
        try {
            const response = await productApi.getAll({ limit: 500 });
            if (response.success) {
                setProducts(response.data);
            }
        } catch (error) {
            console.error('Error fetching products:', error);
        }
    };

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    const openModal = (type, order = null) => {
        setModalType(type);
        setSelectedOrder(order);
        setShowModal(true);
        setErrors({});
        
        if (type === 'create') {
            setFormData({
                customer_id: '',
                product_id: '',
                quantity: 1,
                unit_price: '',
                status_order: 'Pending'
            });
        } else if (type === 'edit' && order) {
            setFormData({
                customer_id: order.customer_id || '',
                product_id: order.product_id || '',
                quantity: order.quantity || 1,
                unit_price: order.unit_price || '',
                status_order: order.status_order || 'Pending'
            });
        }
    };

    const closeModal = () => {
        setShowModal(false);
        setSelectedOrder(null);
        setFormData({
            customer_id: '',
            product_id: '',
            quantity: 1,
            unit_price: '',
            status_order: 'Pending'
        });
        setErrors({});
    };

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.product_id) {
            newErrors.product_id = 'Product is required';
        }
        
        if (!formData.quantity || parseInt(formData.quantity) <= 0) {
            newErrors.quantity = 'Valid quantity is required';
        }
        
        if (!formData.unit_price || parseFloat(formData.unit_price) <= 0) {
            newErrors.unit_price = 'Valid price is required';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        try {
            setSubmitLoading(true);
            
            const submitData = {
                ...formData,
                customer_id: formData.customer_id || null
            };
            
            if (modalType === 'create') {
                await orderApi.create(submitData);
            } else if (modalType === 'edit') {
                await orderApi.update(selectedOrder.order_id, submitData);
            }
            
            await fetchOrders();
            closeModal();
        } catch (error) {
            console.error('Error saving order:', error);
            setErrors({ submit: error.message || 'An error occurred while saving' });
        } finally {
            setSubmitLoading(false);
        }
    };

    const handleDelete = async (orderId) => {
        if (!window.confirm('Are you sure you want to delete this order?')) {
            return;
        }
        
        try {
            await orderApi.delete(orderId);
            await fetchOrders();
        } catch (error) {
            console.error('Error deleting order:', error);
            alert('Error deleting order: ' + (error.message || 'Unknown error'));
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'Completed': return 'bg-green-100 text-green-800';
            case 'Pending': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const handleProductChange = (productId) => {
        const selectedProduct = products.find(p => p.product_id === parseInt(productId));
        setFormData({
            ...formData,
            product_id: productId,
            unit_price: selectedProduct ? selectedProduct.unit_price : ''
        });
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                    <ShoppingBag className="w-8 h-8 text-orange-600" />
                    <h2 className="text-2xl font-bold text-gray-900">Order Management</h2>
                </div>
                <button
                    onClick={() => openModal('create')}
                    className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 flex items-center space-x-2 transition-colors"
                >
                    <Plus className="w-4 h-4" />
                    <span>Add Order</span>
                </button>
            </div>

            {/* Search */}
            <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                        type="text"
                        placeholder="Search orders by customer or product..."
                        value={searchTerm}
                        onChange={handleSearch}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                </div>
            </div>

            {/* Order Table */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                {loading ? (
                    <div className="flex items-center justify-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
                    </div>
                ) : (
                    <>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Order Details
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Customer & Product
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Quantity & Price
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status & Date
                                        </th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {orders.map((order) => (
                                        <tr key={order.order_id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                                        <ShoppingBag className="w-5 h-5 text-orange-600" />
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            Order #{order.order_id}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            ID: {order.order_id}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900 flex items-center">
                                                    <User className="w-4 h-4 mr-2 text-gray-400" />
                                                    {order.customer_id ? `Customer #${order.customer_id}` : 'Walk-in Customer'}
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center mt-1">
                                                    <Package className="w-4 h-4 mr-2 text-gray-400" />
                                                    Product #{order.product_id}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    Qty: {order.quantity}
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center">
                                                    <DollarSign className="w-4 h-4 mr-1 text-gray-400" />
                                                    ${parseFloat(order.unit_price || 0).toFixed(2)} each
                                                </div>
                                                <div className="text-sm font-medium text-gray-900">
                                                    Total: ${(parseFloat(order.unit_price || 0) * parseInt(order.quantity || 0)).toFixed(2)}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900 flex items-center">
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status_order)}`}>
                                                        {order.status_order}
                                                    </span>
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center mt-1">
                                                    <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                                                    {new Date(order.order_date).toLocaleDateString()}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <button
                                                        onClick={() => openModal('view', order)}
                                                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                        title="View"
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => openModal('edit', order)}
                                                        className="text-green-600 hover:text-green-900 p-1 rounded"
                                                        title="Edit"
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => handleDelete(order.order_id)}
                                                        className="text-red-600 hover:text-red-900 p-1 rounded"
                                                        title="Delete"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                                <div className="flex-1 flex justify-between sm:hidden">
                                    <button
                                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                        disabled={currentPage === 1}
                                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                        disabled={currentPage === totalPages}
                                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Next
                                    </button>
                                </div>
                                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p className="text-sm text-gray-700">
                                            Page <span className="font-medium">{currentPage}</span> of{' '}
                                            <span className="font-medium">{totalPages}</span>
                                        </p>
                                    </div>
                                    <div>
                                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                            <button
                                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                                disabled={currentPage === 1}
                                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Previous
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                                disabled={currentPage === totalPages}
                                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Next
                                            </button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* Modal */}
            <AnimatePresence>
                {showModal && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                    >
                        <motion.div
                            initial={{ scale: 0.95, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.95, opacity: 0 }}
                            className="bg-white rounded-lg shadow-xl w-full max-w-lg"
                        >
                            <div className="flex justify-between items-center p-6 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {modalType === 'view' ? 'Order Details' :
                                     modalType === 'edit' ? 'Edit Order' : 'Create Order'}
                                </h3>
                                <button
                                    onClick={closeModal}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>

                            <div className="p-6">
                                {modalType === 'view' && selectedOrder ? (
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Order ID</label>
                                            <p className="text-gray-900">#{selectedOrder.order_id}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                                            <p className="text-gray-900">{selectedOrder.customer_id ? `Customer #${selectedOrder.customer_id}` : 'Walk-in Customer'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Product</label>
                                            <p className="text-gray-900">Product #{selectedOrder.product_id}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                                            <p className="text-gray-900">{selectedOrder.quantity}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Unit Price</label>
                                            <p className="text-gray-900">${parseFloat(selectedOrder.unit_price || 0).toFixed(2)}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Total Amount</label>
                                            <p className="text-gray-900 font-semibold">${(parseFloat(selectedOrder.unit_price || 0) * parseInt(selectedOrder.quantity || 0)).toFixed(2)}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedOrder.status_order)}`}>
                                                {selectedOrder.status_order}
                                            </span>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Order Date</label>
                                            <p className="text-gray-900">{new Date(selectedOrder.order_date).toLocaleDateString()}</p>
                                        </div>
                                    </div>
                                ) : (
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        {errors.submit && (
                                            <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center">
                                                <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                                                <span className="text-red-700 text-sm">{errors.submit}</span>
                                            </div>
                                        )}

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Customer (Optional)
                                            </label>
                                            <select
                                                value={formData.customer_id}
                                                onChange={(e) => setFormData({...formData, customer_id: e.target.value})}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                            >
                                                <option value="">Walk-in Customer</option>
                                                {customers.map(customer => (
                                                    <option key={customer.customer_id} value={customer.customer_id}>
                                                        {customer.first_name} {customer.last_name} - {customer.email}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Product *
                                            </label>
                                            <select
                                                value={formData.product_id}
                                                onChange={(e) => handleProductChange(e.target.value)}
                                                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                                                    errors.product_id ? 'border-red-300' : 'border-gray-300'
                                                }`}
                                            >
                                                <option value="">Select a product</option>
                                                {products.map(product => (
                                                    <option key={product.product_id} value={product.product_id}>
                                                        {product.product_name} - ${parseFloat(product.unit_price || 0).toFixed(2)}
                                                    </option>
                                                ))}
                                            </select>
                                            {errors.product_id && (
                                                <p className="text-red-500 text-xs mt-1">{errors.product_id}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Quantity *
                                                </label>
                                                <input
                                                    type="number"
                                                    min="1"
                                                    value={formData.quantity}
                                                    onChange={(e) => setFormData({...formData, quantity: e.target.value})}
                                                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                                                        errors.quantity ? 'border-red-300' : 'border-gray-300'
                                                    }`}
                                                    placeholder="1"
                                                />
                                                {errors.quantity && (
                                                    <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Unit Price *
                                                </label>
                                                <input
                                                    type="number"
                                                    step="0.01"
                                                    value={formData.unit_price}
                                                    onChange={(e) => setFormData({...formData, unit_price: e.target.value})}
                                                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                                                        errors.unit_price ? 'border-red-300' : 'border-gray-300'
                                                    }`}
                                                    placeholder="0.00"
                                                />
                                                {errors.unit_price && (
                                                    <p className="text-red-500 text-xs mt-1">{errors.unit_price}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Status
                                            </label>
                                            <select
                                                value={formData.status_order}
                                                onChange={(e) => setFormData({...formData, status_order: e.target.value})}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                            >
                                                {orderStatuses.map(status => (
                                                    <option key={status} value={status}>{status}</option>
                                                ))}
                                            </select>
                                        </div>

                                        {formData.quantity && formData.unit_price && (
                                            <div className="bg-gray-50 p-3 rounded-md">
                                                <div className="text-sm text-gray-600">Total Amount:</div>
                                                <div className="text-lg font-semibold text-gray-900">
                                                    ${(parseFloat(formData.unit_price || 0) * parseInt(formData.quantity || 0)).toFixed(2)}
                                                </div>
                                            </div>
                                        )}

                                        <div className="flex justify-end space-x-3 pt-4">
                                            <button
                                                type="button"
                                                onClick={closeModal}
                                                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={submitLoading}
                                                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 flex items-center space-x-2 transition-colors"
                                            >
                                                {submitLoading ? (
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                                ) : (
                                                    <Save className="w-4 h-4" />
                                                )}
                                                <span>{modalType === 'edit' ? 'Update' : 'Create'}</span>
                                            </button>
                                        </div>
                                    </form>
                                )}
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export default OrderManagement;
