import React, { useState } from 'react';
import { api } from '../../services/api.js';
import DataTable from '../common/DataTable';

const OrderManagement = () => {
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create

    const columns = [
        {
            key: 'order_id',
            label: 'Order ID',
            sortable: true
        },
        {
            key: 'customer_name',
            label: 'Customer',
            sortable: true
        },
        {
            key: 'employee_name',
            label: 'Employee',
            sortable: true
        },
        {
            key: 'total_amount',
            label: 'Total Amount',
            sortable: true,
            render: (value) => `$${parseFloat(value).toLocaleString()}`
        },
        {
            key: 'status',
            label: 'Status',
            sortable: true,
            render: (value) => (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    value === 'completed' ? 'bg-green-100 text-green-800' :
                    value === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    value === 'cancelled' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                }`}>
                    {value}
                </span>
            )
        },
        {
            key: 'payment_status',
            label: 'Payment',
            sortable: true,
            render: (value) => (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    value === 'paid' ? 'bg-green-100 text-green-800' :
                    value === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    value === 'failed' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                }`}>
                    {value || 'unpaid'}
                </span>
            )
        },
        {
            key: 'order_date',
            label: 'Order Date',
            sortable: true,
            render: (value) => new Date(value).toLocaleDateString()
        },
        {
            key: 'delivery_date',
            label: 'Delivery Date',
            sortable: true,
            render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
        }
    ];

    const actions = [
        {
            label: 'View',
            onClick: (order) => {
                setSelectedOrder(order);
                setModalType('view');
                setShowModal(true);
            },
            className: 'text-blue-600 hover:text-blue-900'
        },
        {
            label: 'Edit',
            onClick: (order) => {
                setSelectedOrder(order);
                setModalType('edit');
                setShowModal(true);
            },
            className: 'text-green-600 hover:text-green-900'
        },
        {
            label: 'Delete',
            onClick: (order) => {
                if (window.confirm(`Are you sure you want to delete order #${order.order_id}?`)) {
                    handleDeleteOrder(order.order_id);
                }
            },
            className: 'text-red-600 hover:text-red-900'
        }
    ];

    const handleDeleteOrder = async (orderId) => {
        try {
            await api.orders.delete(orderId);
            // Refresh the table data
            window.location.reload();
        } catch (error) {
            console.error('Error deleting order:', error);
            alert('Failed to delete order');
        }
    };

    const fetchOrders = async (params) => {
        try {
            const response = await api.orders.getAll(params);
            return response;
        } catch (error) {
            console.error('Error fetching orders:', error);
            throw error;
        }
    };

    const OrderModal = () => {
        if (!showModal) return null;

        return (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div className="mt-3">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">
                                {modalType === 'view' ? 'Order Details' : 
                                 modalType === 'edit' ? 'Edit Order' : 'Create Order'}
                            </h3>
                            <button
                                onClick={() => setShowModal(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        {selectedOrder && (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Order ID</label>
                                    <div className="mt-1 text-sm text-gray-900">#{selectedOrder.order_id}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Customer</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedOrder.customer_name}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Employee</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedOrder.employee_name}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Total Amount</label>
                                    <div className="mt-1 text-sm text-gray-900">
                                        ${parseFloat(selectedOrder.total_amount).toLocaleString()}
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Status</label>
                                    <div className="mt-1">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                            selectedOrder.status === 'completed' ? 'bg-green-100 text-green-800' :
                                            selectedOrder.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                            selectedOrder.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                            'bg-gray-100 text-gray-800'
                                        }`}>
                                            {selectedOrder.status}
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Payment Status</label>
                                    <div className="mt-1">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                            selectedOrder.payment_status === 'paid' ? 'bg-green-100 text-green-800' :
                                            selectedOrder.payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                            selectedOrder.payment_status === 'failed' ? 'bg-red-100 text-red-800' :
                                            'bg-gray-100 text-gray-800'
                                        }`}>
                                            {selectedOrder.payment_status || 'unpaid'}
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Order Date</label>
                                    <div className="mt-1 text-sm text-gray-900">
                                        {new Date(selectedOrder.order_date).toLocaleString()}
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Delivery Date</label>
                                    <div className="mt-1 text-sm text-gray-900">
                                        {selectedOrder.delivery_date ? new Date(selectedOrder.delivery_date).toLocaleString() : 'N/A'}
                                    </div>
                                </div>
                            </div>
                        )}
                        
                        <div className="mt-6 flex justify-end space-x-3">
                            <button
                                onClick={() => setShowModal(false)}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">Order Management</h2>
                <button
                    onClick={() => {
                        setSelectedOrder(null);
                        setModalType('create');
                        setShowModal(true);
                    }}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                >
                    Create Order
                </button>
            </div>

            <DataTable
                title="Orders"
                columns={columns}
                fetchData={fetchOrders}
                actions={actions}
                searchPlaceholder="Search orders..."
                defaultSort={{ field: 'order_id', order: 'desc' }}
            />

            <OrderModal />
        </div>
    );
};

export default OrderManagement;
