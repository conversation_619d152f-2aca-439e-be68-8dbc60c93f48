import express from 'express';
import AnalyticsController from '../controllers/analyticsController.js';

const router = express.Router();

/**
 * @swagger
 * /api/analytics/loyalty-points:
 *   get:
 *     summary: Get loyalty points per customer
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Loyalty points per customer retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       customer_id:
 *                         type: integer
 *                       customer_name:
 *                         type: string
 *                       total_points:
 *                         type: integer
 */
router.get('/loyalty-points', AnalyticsController.getLoyaltyPointsPerCustomer);

/**
 * @swagger
 * /api/analytics/material-costs:
 *   get:
 *     summary: Get material costs per month (eating and object materials)
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Material costs per month retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       material_type:
 *                         type: string
 *                         enum: [eating, object]
 *                       month:
 *                         type: string
 *                       total_cost:
 *                         type: number
 */
router.get('/material-costs', AnalyticsController.getMaterialCostsPerMonth);

/**
 * @swagger
 * /api/analytics/zero-quantity-products:
 *   get:
 *     summary: Get products with zero quantity materials
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Products with zero quantity materials retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       product_name:
 *                         type: string
 *                       material_eating:
 *                         type: string
 *                       material_object:
 *                         type: string
 *                       eating_quantity:
 *                         type: integer
 *                       object_quantity:
 *                         type: integer
 */
router.get('/zero-quantity-products', AnalyticsController.getProductsWithZeroQuantityMaterials);

/**
 * @swagger
 * /api/analytics/sales/recent:
 *   get:
 *     summary: Get sales in last N days
 *     tags: [Analytics]
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *         description: Number of days to retrieve sales for
 *     responses:
 *       200:
 *         description: Recent sales retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date
 *                       total_sales:
 *                         type: number
 *                       order_count:
 *                         type: integer
 */
router.get('/sales/recent', AnalyticsController.getSalesLastDays);

/**
 * @swagger
 * /api/analytics/products/most-sold:
 *   get:
 *     summary: Get product sold the most (by quantity)
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Most sold product retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     product_name:
 *                       type: string
 *                     total_quantity_sold:
 *                       type: integer
 */
router.get('/products/most-sold', AnalyticsController.getMostSoldProduct);

/**
 * @swagger
 * /api/analytics/suppliers/spending:
 *   get:
 *     summary: Get spending on supplies (grouped by supplier)
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Supplier spending retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       supplies_name:
 *                         type: string
 *                       category:
 *                         type: string
 *                       total_spent:
 *                         type: number
 */
router.get('/suppliers/spending', AnalyticsController.getSupplierSpending);

/**
 * @swagger
 * /api/analytics/customers/order-history:
 *   get:
 *     summary: Get customer order history
 *     tags: [Analytics]
 *     parameters:
 *       - in: query
 *         name: customer_id
 *         schema:
 *           type: integer
 *         description: Filter by specific customer ID
 *     responses:
 *       200:
 *         description: Customer order history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       customer_id:
 *                         type: integer
 *                       customer_name:
 *                         type: string
 *                       order_id:
 *                         type: integer
 *                       product_name:
 *                         type: string
 *                       quantity:
 *                         type: integer
 *                       unit_price:
 *                         type: number
 *                       order_date:
 *                         type: string
 *                         format: date-time
 *                       total_amount:
 *                         type: number
 */
router.get('/customers/order-history', AnalyticsController.getCustomerOrderHistory);

/**
 * @swagger
 * /api/analytics/sales/monthly:
 *   get:
 *     summary: Get sales by month
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Monthly sales retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       month:
 *                         type: string
 *                       total_sales:
 *                         type: number
 *                       order_count:
 *                         type: integer
 */
router.get('/sales/monthly', AnalyticsController.getSalesByMonth);

/**
 * @swagger
 * /api/analytics/revenue/by-product:
 *   get:
 *     summary: Get total revenue by product
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Revenue by product retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       product_name:
 *                         type: string
 *                       category_name:
 *                         type: string
 *                       total_revenue:
 *                         type: number
 *                       total_quantity_sold:
 *                         type: integer
 */
router.get('/revenue/by-product', AnalyticsController.getRevenueByProduct);

/**
 * @swagger
 * /api/analytics/customers/top-spenders:
 *   get:
 *     summary: Get top customers by spending
 *     tags: [Analytics]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 5
 *         description: Number of top customers to retrieve
 *     responses:
 *       200:
 *         description: Top customers by spending retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       customer_id:
 *                         type: integer
 *                       customer_name:
 *                         type: string
 *                       email:
 *                         type: string
 *                       total_spent:
 *                         type: number
 *                       total_orders:
 *                         type: integer
 */
router.get('/customers/top-spenders', AnalyticsController.getTopCustomersBySpending);

/**
 * @swagger
 * /api/analytics/products/unused:
 *   get:
 *     summary: Get unused products (never ordered)
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Unused products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       product_id:
 *                         type: integer
 *                       product_name:
 *                         type: string
 *                       category_name:
 *                         type: string
 *                       unit_price:
 *                         type: number
 */
router.get('/products/unused', AnalyticsController.getUnusedProducts);

/**
 * @swagger
 * /api/analytics/products/most-frequent:
 *   get:
 *     summary: Get most frequently ordered product (by order count)
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Most frequently ordered product retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     product_name:
 *                       type: string
 *                     category_name:
 *                       type: string
 *                     times_ordered:
 *                       type: integer
 *                     total_quantity:
 *                       type: integer
 */
router.get('/products/most-frequent', AnalyticsController.getMostFrequentlyOrderedProduct);

/**
 * @swagger
 * /api/analytics/orders/monthly-stats:
 *   get:
 *     summary: Get monthly order count and revenue statistics
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Monthly order statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       month:
 *                         type: string
 *                       total_orders:
 *                         type: integer
 *                       total_revenue:
 *                         type: number
 *                       average_order_value:
 *                         type: number
 */
router.get('/orders/monthly-stats', AnalyticsController.getMonthlyOrderStats);

/**
 * @swagger
 * /api/analytics/orders/no-payment:
 *   get:
 *     summary: Get orders with no payment
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Orders with no payment retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       order_id:
 *                         type: integer
 *                       first_name:
 *                         type: string
 *                       last_name:
 *                         type: string
 *                       product_name:
 *                         type: string
 *                       quantity:
 *                         type: integer
 *                       unit_price:
 *                         type: number
 *                       order_date:
 *                         type: string
 *                         format: date-time
 *                       total_amount:
 *                         type: number
 */
router.get('/orders/no-payment', AnalyticsController.getOrdersWithNoPayment);

/**
 * @swagger
 * /api/analytics/materials/stock-levels:
 *   get:
 *     summary: Get material stock levels
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Material stock levels retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                         enum: [eating, object]
 *                       name_material:
 *                         type: string
 *                       quantity:
 *                         type: integer
 */
router.get('/materials/stock-levels', AnalyticsController.getMaterialStockLevels);

/**
 * @swagger
 * /api/analytics/products/raw-material-cost:
 *   get:
 *     summary: Get raw material cost per product
 *     tags: [Analytics]
 *     responses:
 *       200:
 *         description: Raw material cost per product retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       product_name:
 *                         type: string
 *                       raw_material_cost:
 *                         type: number
 */
router.get('/products/raw-material-cost', AnalyticsController.getRawMaterialCostPerProduct);

export default router;
