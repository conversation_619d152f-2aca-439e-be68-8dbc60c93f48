import React from 'react';

const LoyaltyPointsChart = ({ data }) => {
    if (!data || data.length === 0) {
        return <div className="text-center text-gray-500 py-8">No loyalty points data available</div>;
    }

    const maxPoints = Math.max(...data.map(item => parseInt(item.total_points || 0)));

    return (
        <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Top Loyalty Points Customers</h3>
            <div className="space-y-3">
                {data.slice(0, 10).map((customer, index) => {
                    const points = parseInt(customer.total_points || 0);
                    const percentage = maxPoints > 0 ? (points / maxPoints) * 100 : 0;
                    
                    return (
                        <div key={customer.customer_id} className="flex items-center space-x-4">
                            <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center text-white font-bold text-sm">
                                {index + 1}
                            </div>
                            <div className="flex-1">
                                <div className="flex items-center justify-between mb-1">
                                    <span className="text-sm font-medium text-gray-900">
                                        {customer.customer_name}
                                    </span>
                                    <span className="text-sm font-bold text-purple-600">
                                        {points} points
                                    </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                        className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${Math.max(2, percentage)}%` }}
                                    />
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default LoyaltyPointsChart;
