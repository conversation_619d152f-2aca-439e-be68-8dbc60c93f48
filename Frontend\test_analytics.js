// Test script to verify analytics endpoints
import { api } from '../src/services/api.js';

const testAnalyticsEndpoints = async () => {
    console.log('🧪 Testing Analytics Endpoints...\n');
    
    const endpoints = [
        { name: 'Loyalty Points', path: '/analytics/loyalty-points' },
        { name: 'Material Costs', path: '/analytics/material-costs' },
        { name: 'Recent Sales', path: '/analytics/sales/recent?days=7' },
        { name: 'Most Sold Product', path: '/analytics/products/most-sold' },
        { name: 'Top Customers', path: '/analytics/customers/top-spenders?limit=5' },
        { name: 'Monthly Sales', path: '/analytics/sales/monthly' },
        { name: 'Revenue by Product', path: '/analytics/revenue/by-product' },
        { name: 'Unused Products', path: '/analytics/products/unused' },
        { name: 'Material Stock Levels', path: '/analytics/materials/stock-levels' },
        { name: 'Orders with No Payment', path: '/analytics/orders/no-payment' },
        { name: 'Supplier Spending', path: '/analytics/suppliers/spending' },
        { name: 'Monthly Order Stats', path: '/analytics/orders/monthly-stats' }
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`Testing ${endpoint.name}...`);
            const response = await fetch(`http://localhost:3000/api${endpoint.path}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ ${endpoint.name}: ${data.data ? data.data.length : 0} records`);
            } else {
                console.log(`❌ ${endpoint.name}: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            console.log(`❌ ${endpoint.name}: ${error.message}`);
        }
    }
    
    console.log('\n🎉 Analytics endpoint testing completed!');
};

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testAnalyticsEndpoints();
}

export default testAnalyticsEndpoints;
