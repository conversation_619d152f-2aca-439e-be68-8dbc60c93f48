import React, { useState, useEffect } from 'react';
import { api } from '../../services/api.js';

const SalesChart = ({ data: propData }) => {
    const [salesData, setSalesData] = useState(propData || []);
    const [monthlySales, setMonthlySales] = useState([]);
    const [loading, setLoading] = useState(!propData);
    const [viewMode, setViewMode] = useState('daily'); // 'daily' or 'monthly'

    useEffect(() => {
        if (!propData) {
            fetchSalesData();
        }
    }, [propData]);

    const fetchSalesData = async () => {
        try {
            setLoading(true);
            const [dailyRes, monthlyRes] = await Promise.all([
                api.get('/analytics/sales/recent?days=7'),
                api.get('/analytics/sales/monthly')
            ]);

            setSalesData(dailyRes.data.data || []);
            setMonthlySales(monthlyRes.data.data || []);
        } catch (error) {
            console.error('Error fetching sales data:', error);
        } finally {
            setLoading(false);
        }
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        if (viewMode === 'daily') {
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        } else {
            return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
        }
    };

    const SimpleBarChart = ({ data, xKey, yKey, title }) => {
        if (!data || data.length === 0) {
            return <div className="text-center text-gray-500 py-8">No data available</div>;
        }

        const maxValue = Math.max(...data.map(item => parseFloat(item[yKey] || 0)));
        const minValue = Math.min(...data.map(item => parseFloat(item[yKey] || 0)));
        const range = maxValue - minValue;

        return (
            <div className="w-full">
                <h3 className="text-lg font-semibold mb-4">{title}</h3>
                <div className="space-y-2">
                    {data.map((item, index) => {
                        const value = parseFloat(item[yKey] || 0);
                        const percentage = range > 0 ? ((value - minValue) / range) * 100 : 50;
                        
                        return (
                            <div key={index} className="flex items-center space-x-3">
                                <div className="w-20 text-sm text-gray-600 text-right">
                                    {formatDate(item[xKey])}
                                </div>
                                <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                                    <div
                                        className="bg-blue-500 h-6 rounded-full transition-all duration-300"
                                        style={{ width: `${Math.max(5, percentage)}%` }}
                                    />
                                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                                        ${value.toLocaleString()}
                                    </div>
                                </div>
                                <div className="w-16 text-sm text-gray-600">
                                    {item.order_count || 0} orders
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    const currentData = viewMode === 'daily' ? salesData : monthlySales;
    const title = viewMode === 'daily' ? 'Daily Sales (Last 7 Days)' : 'Monthly Sales Trend';

    return (
        <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-gray-900">Sales Analytics</h2>
                <div className="flex space-x-2">
                    <button
                        onClick={() => setViewMode('daily')}
                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                            viewMode === 'daily'
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                    >
                        Daily
                    </button>
                    <button
                        onClick={() => setViewMode('monthly')}
                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                            viewMode === 'monthly'
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                    >
                        Monthly
                    </button>
                </div>
            </div>

            <SimpleBarChart
                data={currentData}
                xKey={viewMode === 'daily' ? 'date' : 'month'}
                yKey="total_sales"
                title={title}
            />

            {/* Summary Stats */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                    <div className="text-sm font-medium text-blue-600">Total Sales</div>
                    <div className="text-2xl font-bold text-blue-900">
                        ${currentData.reduce((sum, item) => sum + parseFloat(item.total_sales || 0), 0).toLocaleString()}
                    </div>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                    <div className="text-sm font-medium text-green-600">Total Orders</div>
                    <div className="text-2xl font-bold text-green-900">
                        {currentData.reduce((sum, item) => sum + parseInt(item.order_count || 0), 0).toLocaleString()}
                    </div>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                    <div className="text-sm font-medium text-purple-600">Avg Order Value</div>
                    <div className="text-2xl font-bold text-purple-900">
                        ${currentData.length > 0 ? (
                            currentData.reduce((sum, item) => sum + parseFloat(item.total_sales || 0), 0) /
                            currentData.reduce((sum, item) => sum + parseInt(item.order_count || 0), 0)
                        ).toFixed(2) : '0.00'}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SalesChart;
