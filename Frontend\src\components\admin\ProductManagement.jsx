import React, { useState } from 'react';
import { api } from '../../services/api.js';
import DataTable from '../common/DataTable';

const ProductManagement = () => {
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create

    const columns = [
        {
            key: 'product_id',
            label: 'ID',
            sortable: true
        },
        {
            key: 'product_name',
            label: 'Product Name',
            sortable: true
        },
        {
            key: 'category',
            label: 'Category',
            sortable: true
        },
        {
            key: 'price',
            label: 'Price',
            sortable: true,
            render: (value) => `$${parseFloat(value).toFixed(2)}`
        },
        {
            key: 'stock_quantity',
            label: 'Stock',
            sortable: true,
            render: (value) => (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    value < 10 ? 'bg-red-100 text-red-800' :
                    value < 50 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                }`}>
                    {value}
                </span>
            )
        },
        {
            key: 'description',
            label: 'Description',
            sortable: false,
            render: (value) => value ? value.substring(0, 50) + '...' : 'N/A'
        },
        {
            key: 'created_at',
            label: 'Created',
            sortable: true,
            render: (value) => new Date(value).toLocaleDateString()
        }
    ];

    const actions = [
        {
            label: 'View',
            onClick: (product) => {
                setSelectedProduct(product);
                setModalType('view');
                setShowModal(true);
            },
            className: 'text-blue-600 hover:text-blue-900'
        },
        {
            label: 'Edit',
            onClick: (product) => {
                setSelectedProduct(product);
                setModalType('edit');
                setShowModal(true);
            },
            className: 'text-green-600 hover:text-green-900'
        },
        {
            label: 'Delete',
            onClick: (product) => {
                if (window.confirm(`Are you sure you want to delete product ${product.product_name}?`)) {
                    handleDeleteProduct(product.product_id);
                }
            },
            className: 'text-red-600 hover:text-red-900'
        }
    ];

    const handleDeleteProduct = async (productId) => {
        try {
            await api.products.delete(productId);
            // Refresh the table data
            window.location.reload();
        } catch (error) {
            console.error('Error deleting product:', error);
            alert('Failed to delete product');
        }
    };

    const fetchProducts = async (params) => {
        try {
            const response = await api.products.getAll(params);
            return response;
        } catch (error) {
            console.error('Error fetching products:', error);
            throw error;
        }
    };

    const ProductModal = () => {
        if (!showModal) return null;

        return (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div className="mt-3">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">
                                {modalType === 'view' ? 'Product Details' : 
                                 modalType === 'edit' ? 'Edit Product' : 'Create Product'}
                            </h3>
                            <button
                                onClick={() => setShowModal(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        {selectedProduct && (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Product Name</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedProduct.product_name}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Category</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedProduct.category}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Price</label>
                                    <div className="mt-1 text-sm text-gray-900">${parseFloat(selectedProduct.price).toFixed(2)}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Stock Quantity</label>
                                    <div className="mt-1">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                            selectedProduct.stock_quantity < 10 ? 'bg-red-100 text-red-800' :
                                            selectedProduct.stock_quantity < 50 ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-green-100 text-green-800'
                                        }`}>
                                            {selectedProduct.stock_quantity}
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Description</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedProduct.description || 'N/A'}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Created</label>
                                    <div className="mt-1 text-sm text-gray-900">
                                        {new Date(selectedProduct.created_at).toLocaleString()}
                                    </div>
                                </div>
                            </div>
                        )}
                        
                        <div className="mt-6 flex justify-end space-x-3">
                            <button
                                onClick={() => setShowModal(false)}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">Product Management</h2>
                <button
                    onClick={() => {
                        setSelectedProduct(null);
                        setModalType('create');
                        setShowModal(true);
                    }}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                >
                    Add Product
                </button>
            </div>

            <DataTable
                title="Products"
                columns={columns}
                fetchData={fetchProducts}
                actions={actions}
                searchPlaceholder="Search products..."
                defaultSort={{ field: 'product_id', order: 'desc' }}
            />

            <ProductModal />
        </div>
    );
};

export default ProductManagement;
