import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
    Coffee, Plus, Search, Edit, Trash2, Eye, X, 
    Package, DollarSign, Tag, FileText, Save, AlertCircle 
} from 'lucide-react';
import { productApi, materialApi } from '../../services/api.js';

const ProductManagement = () => {
    const [products, setProducts] = useState([]);
    const [materials, setMaterials] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [formData, setFormData] = useState({
        product_name: '',
        category_name: 'Coffee',
        description: '',
        unit_price: '',
        material_eating_id: '',
        material_object_id: ''
    });
    const [errors, setErrors] = useState({});
    const [submitLoading, setSubmitLoading] = useState(false);

    const categories = ['Coffee', 'Tea', 'Pastry', 'Sandwich', 'Juice', 'Other'];

    useEffect(() => {
        fetchProducts();
        fetchMaterials();
    }, [currentPage, searchTerm]);

    const fetchProducts = async () => {
        try {
            setLoading(true);
            const response = await productApi.getAll({
                page: currentPage,
                limit: 10,
                search: searchTerm
            });
            
            if (response.success) {
                setProducts(response.data);
                setTotalPages(response.pagination?.totalPages || 1);
            }
        } catch (error) {
            console.error('Error fetching products:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchMaterials = async () => {
        try {
            const response = await materialApi.getAll({ limit: 500 });
            if (response.success) {
                setMaterials(response.data);
            }
        } catch (error) {
            console.error('Error fetching materials:', error);
        }
    };

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    const openModal = (type, product = null) => {
        setModalType(type);
        setSelectedProduct(product);
        setShowModal(true);
        setErrors({});
        
        if (type === 'create') {
            setFormData({
                product_name: '',
                category_name: 'Coffee',
                description: '',
                unit_price: '',
                material_eating_id: '',
                material_object_id: ''
            });
        } else if (type === 'edit' && product) {
            setFormData({
                product_name: product.product_name || '',
                category_name: product.category_name || 'Coffee',
                description: product.description || '',
                unit_price: product.unit_price || '',
                material_eating_id: product.material_eating_id || '',
                material_object_id: product.material_object_id || ''
            });
        }
    };

    const closeModal = () => {
        setShowModal(false);
        setSelectedProduct(null);
        setFormData({
            product_name: '',
            category_name: 'Coffee',
            description: '',
            unit_price: '',
            material_eating_id: '',
            material_object_id: ''
        });
        setErrors({});
    };

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.product_name.trim()) {
            newErrors.product_name = 'Product name is required';
        }
        
        if (!formData.unit_price || parseFloat(formData.unit_price) <= 0) {
            newErrors.unit_price = 'Valid price is required';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        try {
            setSubmitLoading(true);
            
            const submitData = {
                ...formData,
                material_eating_id: formData.material_eating_id || null,
                material_object_id: formData.material_object_id || null
            };
            
            if (modalType === 'create') {
                await productApi.create(submitData);
            } else if (modalType === 'edit') {
                await productApi.update(selectedProduct.product_id, submitData);
            }
            
            await fetchProducts();
            closeModal();
        } catch (error) {
            console.error('Error saving product:', error);
            setErrors({ submit: error.message || 'An error occurred while saving' });
        } finally {
            setSubmitLoading(false);
        }
    };

    const handleDelete = async (productId) => {
        if (!window.confirm('Are you sure you want to delete this product?')) {
            return;
        }
        
        try {
            await productApi.delete(productId);
            await fetchProducts();
        } catch (error) {
            console.error('Error deleting product:', error);
            alert('Error deleting product: ' + (error.message || 'Unknown error'));
        }
    };

    const getCategoryColor = (category) => {
        switch (category) {
            case 'Coffee': return 'bg-amber-100 text-amber-800';
            case 'Tea': return 'bg-green-100 text-green-800';
            case 'Pastry': return 'bg-pink-100 text-pink-800';
            case 'Sandwich': return 'bg-orange-100 text-orange-800';
            case 'Juice': return 'bg-purple-100 text-purple-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                    <Coffee className="w-8 h-8 text-emerald-600" />
                    <h2 className="text-2xl font-bold text-gray-900">Product Management</h2>
                </div>
                <button
                    onClick={() => openModal('create')}
                    className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 flex items-center space-x-2 transition-colors"
                >
                    <Plus className="w-4 h-4" />
                    <span>Add Product</span>
                </button>
            </div>

            {/* Search */}
            <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                        type="text"
                        placeholder="Search products by name or category..."
                        value={searchTerm}
                        onChange={handleSearch}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                    />
                </div>
            </div>

            {/* Product Table */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                {loading ? (
                    <div className="flex items-center justify-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"></div>
                    </div>
                ) : (
                    <>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Product
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Category & Price
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description
                                        </th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {products.map((product) => (
                                        <tr key={product.product_id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center">
                                                        <Package className="w-5 h-5 text-emerald-600" />
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {product.product_name}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            ID: {product.product_id}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900 flex items-center">
                                                    <Tag className="w-4 h-4 mr-2 text-gray-400" />
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(product.category_name)}`}>
                                                        {product.category_name}
                                                    </span>
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center mt-1">
                                                    <DollarSign className="w-4 h-4 mr-2 text-gray-400" />
                                                    ${parseFloat(product.unit_price || 0).toFixed(2)}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="text-sm text-gray-900 flex items-start">
                                                    <FileText className="w-4 h-4 mr-2 text-gray-400 mt-0.5" />
                                                    <span className="line-clamp-2">
                                                        {product.description || 'No description available'}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <button
                                                        onClick={() => openModal('view', product)}
                                                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                        title="View"
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => openModal('edit', product)}
                                                        className="text-green-600 hover:text-green-900 p-1 rounded"
                                                        title="Edit"
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => handleDelete(product.product_id)}
                                                        className="text-red-600 hover:text-red-900 p-1 rounded"
                                                        title="Delete"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                                <div className="flex-1 flex justify-between sm:hidden">
                                    <button
                                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                        disabled={currentPage === 1}
                                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                        disabled={currentPage === totalPages}
                                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                    >
                                        Next
                                    </button>
                                </div>
                                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p className="text-sm text-gray-700">
                                            Page <span className="font-medium">{currentPage}</span> of{' '}
                                            <span className="font-medium">{totalPages}</span>
                                        </p>
                                    </div>
                                    <div>
                                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                            <button
                                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                                disabled={currentPage === 1}
                                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Previous
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                                disabled={currentPage === totalPages}
                                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                            >
                                                Next
                                            </button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* Modal */}
            <AnimatePresence>
                {showModal && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                    >
                        <motion.div
                            initial={{ scale: 0.95, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0.95, opacity: 0 }}
                            className="bg-white rounded-lg shadow-xl w-full max-w-lg"
                        >
                            <div className="flex justify-between items-center p-6 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {modalType === 'view' ? 'Product Details' :
                                     modalType === 'edit' ? 'Edit Product' : 'Create Product'}
                                </h3>
                                <button
                                    onClick={closeModal}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>

                            <div className="p-6">
                                {modalType === 'view' && selectedProduct ? (
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
                                            <p className="text-gray-900">{selectedProduct.product_name}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                                            <p className="text-gray-900">{selectedProduct.category_name}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
                                            <p className="text-gray-900">${parseFloat(selectedProduct.unit_price || 0).toFixed(2)}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                            <p className="text-gray-900">{selectedProduct.description || 'No description available'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Material Eating ID</label>
                                            <p className="text-gray-900">{selectedProduct.material_eating_id || 'Not assigned'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Material Object ID</label>
                                            <p className="text-gray-900">{selectedProduct.material_object_id || 'Not assigned'}</p>
                                        </div>
                                    </div>
                                ) : (
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        {errors.submit && (
                                            <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center">
                                                <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                                                <span className="text-red-700 text-sm">{errors.submit}</span>
                                            </div>
                                        )}

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Product Name *
                                            </label>
                                            <input
                                                type="text"
                                                value={formData.product_name}
                                                onChange={(e) => setFormData({...formData, product_name: e.target.value})}
                                                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-transparent ${
                                                    errors.product_name ? 'border-red-300' : 'border-gray-300'
                                                }`}
                                                placeholder="Enter product name"
                                            />
                                            {errors.product_name && (
                                                <p className="text-red-500 text-xs mt-1">{errors.product_name}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Category *
                                                </label>
                                                <select
                                                    value={formData.category_name}
                                                    onChange={(e) => setFormData({...formData, category_name: e.target.value})}
                                                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-transparent ${
                                                        errors.category_name ? 'border-red-300' : 'border-gray-300'
                                                    }`}
                                                >
                                                    {categories.map(category => (
                                                        <option key={category} value={category}>{category}</option>
                                                    ))}
                                                </select>
                                                {errors.category_name && (
                                                    <p className="text-red-500 text-xs mt-1">{errors.category_name}</p>
                                                )}
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Price *
                                                </label>
                                                <input
                                                    type="number"
                                                    step="0.01"
                                                    value={formData.unit_price}
                                                    onChange={(e) => setFormData({...formData, unit_price: e.target.value})}
                                                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-transparent ${
                                                        errors.unit_price ? 'border-red-300' : 'border-gray-300'
                                                    }`}
                                                    placeholder="0.00"
                                                />
                                                {errors.unit_price && (
                                                    <p className="text-red-500 text-xs mt-1">{errors.unit_price}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Description
                                            </label>
                                            <textarea
                                                value={formData.description}
                                                onChange={(e) => setFormData({...formData, description: e.target.value})}
                                                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-transparent ${
                                                    errors.description ? 'border-red-300' : 'border-gray-300'
                                                }`}
                                                placeholder="Enter product description"
                                                rows="3"
                                            />
                                            {errors.description && (
                                                <p className="text-red-500 text-xs mt-1">{errors.description}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Material Eating ID
                                                </label>
                                                <select
                                                    value={formData.material_eating_id}
                                                    onChange={(e) => setFormData({...formData, material_eating_id: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                                >
                                                    <option value="">Select material (optional)</option>
                                                    {materials.filter(m => m.type === 'eating').map(material => (
                                                        <option key={material.id} value={material.id}>
                                                            {material.name} - {material.category}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Material Object ID
                                                </label>
                                                <select
                                                    value={formData.material_object_id}
                                                    onChange={(e) => setFormData({...formData, material_object_id: e.target.value})}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                                >
                                                    <option value="">Select material (optional)</option>
                                                    {materials.filter(m => m.type === 'object').map(material => (
                                                        <option key={material.id} value={material.id}>
                                                            {material.name} - {material.category}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>

                                        <div className="flex justify-end space-x-3 pt-4">
                                            <button
                                                type="button"
                                                onClick={closeModal}
                                                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                disabled={submitLoading}
                                                className="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 disabled:opacity-50 flex items-center space-x-2 transition-colors"
                                            >
                                                {submitLoading ? (
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                                ) : (
                                                    <Save className="w-4 h-4" />
                                                )}
                                                <span>{modalType === 'edit' ? 'Update' : 'Create'}</span>
                                            </button>
                                        </div>
                                    </form>
                                )}
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export default ProductManagement;
