

import mysql from 'mysql2/promise';
import fs from 'fs';
import dotenv from "dotenv";
dotenv.config();

// Create connection pool for Aiven database
let aivenPool = null;

function getAivenPool() {
  if (aivenPool) return aivenPool;
  
  // Try to find ca.pem in config, then in project root
  let caPath = 'ca.pem';
  let ca;
  try {
    ca = fs.readFileSync(new URL(caPath, import.meta.url));
  } catch (e) {
    try {
      ca = fs.readFileSync(new URL('../../ca.pem', import.meta.url));
    } catch (e2) {
      throw new Error('ca.pem not found in ./config or project root.');
    }
  }

  aivenPool = mysql.createPool({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER || 'admin_role',
    password: process.env.DB_PASSWORD || '123',
    
    database: process.env.DB_NAME,
    ssl: {
      ca: ca
    },
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
  });
  const pro = process.env.DB_USER || 'admin_role';
  console.log(`\n ${pro}`);
  return aivenPool;
}

export async function connectToAivenDB() {
  const pool = getAivenPool();
  return await pool.getConnection();
}

// console.log("Connected to MySQL database!");
// console.log(process.env.DB_HOST);
// console.log(process.env.DB_USER);
// await connectToAivenDB();












// sql connector with cloud server AIven
// const Valkey = require('ioredis');
// import Valkey from 'ioredis';
// const serviceUri = 'mysql://avnadmin:<EMAIL>:21011/defaultdb?ssl-mode=REQUIRED';
// const valkey = new Valkey(serviceUri);

// valkey.set('key', 'hello world');

// valkey.get('key').then( (result) => {
//   console.log(`The value of key is: ${result}`);
//   valkey.disconnect();
// });

// import mysql from 'mysql2/promise';
// import fs from 'fs';
// import dotenv from "dotenv";
// dotenv.config();

// const ca = fs.readFileSync(new URL('../../ca.pem', import.meta.url));

// const connection = await mysql.createConnection({
//   host: 'coffe-management-db1st-choengrayu307-607c.b.aivencloud.com',
//   port: 21011,
//   user: 'avnadmin',
//   password: 'AVNS_KiWXNwxtH4tpuvcCgbt',
//   database: 'defaultdb',
//   ssl: {
//     ca: ca
//   }
// });

// console.log('Connected to MySQL database!');

// // Example query
// const [rows] = await connection.execute('SELECT 1 + 1 AS solution');
// console.log('Test query result:', rows);

// await connection.end();



// // ...existing code...
// export async function getConnection() {
//   const ca = fs.readFileSync(new URL('../../ca.pem', import.meta.url));
//   return mysql.createConnection({
//     host: 'coffe-management-db1st-choengrayu307-607c.b.aivencloud.com',
//     port: 21011,
//     user: 'avnadmin',
//     password: 'AVNS_KiWXNwxtH4tpuvcCgbt',
//     database: 'Week6DB',
//     ssl: { ca }
//   });
// }


