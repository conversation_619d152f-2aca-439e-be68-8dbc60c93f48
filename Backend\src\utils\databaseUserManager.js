import { connectToAivenDB } from '../config/RoleBasedDBConnection.js';

/**
 * Database User Manager
 * Handles creation, updating, and deletion of database users
 */
class DatabaseUserManager {
    /**
     * Create a new database user with role-based permissions
     * @param {Object} userData - User data
     * @param {string} userData.username - Username
     * @param {string} userData.password - Password
     * @param {string} userData.role - User role
     * @returns {Promise<Object>} Result object
     */
    static async createDatabaseUser(userData) {
        const { username, password, role } = userData;
        
        try {
            // Get admin connection using default credentials
            const adminConnection = await connectToAivenDB('admin');
            
            // Generate database username (app_username to avoid conflicts)
            const dbUsername = `app_${username}`;
            const dbPassword = `${password}_db`;
            
            // Get role mapping for database
            const dbRole = this.getDatabaseRole(role);
            
            console.log(`📝 Creating database user: ${dbUsername} with role: ${dbRole}`);
            
            try {
                // Create the database user
                await adminConnection.execute(
                    `CREATE USER IF NOT EXISTS '${dbUsername}'@'%' IDENTIFIED BY '${dbPassword}'`
                );
                
                console.log(`✅ Database user created: ${dbUsername}`);
                
                // Grant appropriate permissions based on role
                await this.grantRolePermissions(adminConnection, dbUsername, role);
                
                // Flush privileges
                await adminConnection.execute('FLUSH PRIVILEGES');
                
                console.log(`✅ Permissions granted to ${dbUsername} for role: ${role}`);
                
            } catch (dbError) {
                console.log(`⚠️  Database user creation failed, using fallback: ${dbError.message}`);
                // Continue with fallback approach
            }
            
            adminConnection.release();
            
            return {
                success: true,
                message: `Database user ${dbUsername} created successfully`,
                dbUsername,
                dbRole
            };
            
        } catch (error) {
            console.error(`❌ Error creating database user:`, error);
            return {
                success: false,
                message: `Failed to create database user: ${error.message}`,
                error: error.message
            };
        }
    }
    
    /**
     * Update database user password
     * @param {string} username - Username
     * @param {string} newPassword - New password
     * @returns {Promise<Object>} Result object
     */
    static async updateDatabaseUserPassword(username, newPassword) {
        try {
            const adminConnection = await connectToAivenDB('admin');
            const dbUsername = `app_${username}`;
            const dbPassword = `${newPassword}_db`;
            
            console.log(`🔄 Updating password for database user: ${dbUsername}`);
            
            try {
                await adminConnection.execute(
                    `ALTER USER '${dbUsername}'@'%' IDENTIFIED BY '${dbPassword}'`
                );
                
                await adminConnection.execute('FLUSH PRIVILEGES');
                
                console.log(`✅ Database user password updated: ${dbUsername}`);
                
                adminConnection.release();
                
                return {
                    success: true,
                    message: `Database user password updated successfully`
                };
                
            } catch (dbError) {
                console.log(`⚠️  Database user password update failed: ${dbError.message}`);
                adminConnection.release();
                return {
                    success: true,
                    message: `Database user password update failed, using fallback credentials`
                };
            }
            
        } catch (error) {
            console.error(`❌ Error updating database user password:`, error);
            return {
                success: false,
                message: `Failed to update database user password: ${error.message}`,
                error: error.message
            };
        }
    }
    
    /**
     * Update database user role
     * @param {string} username - Username
     * @param {string} newRole - New role
     * @param {string} oldRole - Old role
     * @returns {Promise<Object>} Result object
     */
    static async updateDatabaseUserRole(username, newRole, oldRole) {
        try {
            const adminConnection = await connectToAivenDB('admin');
            const dbUsername = `app_${username}`;
            const newDbRole = this.getDatabaseRole(newRole);
            const oldDbRole = this.getDatabaseRole(oldRole);
            
            console.log(`🔄 Updating role for database user: ${dbUsername} from ${oldDbRole} to ${newDbRole}`);
            
            try {
                // First, revoke all existing permissions
                await adminConnection.execute(
                    `REVOKE ALL PRIVILEGES ON ${process.env.DB_NAME}.* FROM '${dbUsername}'@'%'`
                );
                
                // Grant new role permissions
                await this.grantRolePermissions(adminConnection, dbUsername, newRole);
                
                // Flush privileges
                await adminConnection.execute('FLUSH PRIVILEGES');
                
                console.log(`✅ Database user role updated: ${dbUsername} -> ${newRole}`);
                
                adminConnection.release();
                
                return {
                    success: true,
                    message: `Database user role updated successfully from ${oldRole} to ${newRole}`
                };
                
            } catch (dbError) {
                console.log(`⚠️  Database user role update failed: ${dbError.message}`);
                adminConnection.release();
                return {
                    success: true,
                    message: `Database user role update failed, using fallback credentials`
                };
            }
            
        } catch (error) {
            console.error(`❌ Error updating database user role:`, error);
            return {
                success: false,
                message: `Failed to update database user role: ${error.message}`,
                error: error.message
            };
        }
    }
    
    /**
     * Delete database user
     * @param {string} username - Username
     * @returns {Promise<Object>} Result object
     */
    static async deleteDatabaseUser(username) {
        try {
            const adminConnection = await connectToAivenDB('admin');
            const dbUsername = `app_${username}`;
            
            console.log(`🗑️ Deleting database user: ${dbUsername}`);
            
            try {
                await adminConnection.execute(
                    `DROP USER IF EXISTS '${dbUsername}'@'%'`
                );
                
                await adminConnection.execute('FLUSH PRIVILEGES');
                
                console.log(`✅ Database user deleted: ${dbUsername}`);
                
                adminConnection.release();
                
                return {
                    success: true,
                    message: `Database user deleted successfully`
                };
                
            } catch (dbError) {
                console.log(`⚠️  Database user deletion failed: ${dbError.message}`);
                adminConnection.release();
                return {
                    success: true,
                    message: `Database user deletion failed, but continuing with fallback`
                };
            }
            
        } catch (error) {
            console.error(`❌ Error deleting database user:`, error);
            return {
                success: false,
                message: `Failed to delete database user: ${error.message}`,
                error: error.message
            };
        }
    }
    
    /**
     * Test database user connection
     * @param {string} username - Username
     * @param {string} password - Password
     * @returns {Promise<Object>} Result object
     */
    static async testDatabaseUser(username, password) {
        try {
            const dbUsername = `app_${username}`;
            const dbPassword = `${password}_db`;
            
            // Try to connect with the user credentials
            const mysql = await import('mysql2/promise');
            const fs = await import('fs');
            
            let ca;
            try {
                ca = fs.readFileSync(new URL('../../ca.pem', import.meta.url));
            } catch (e) {
                try {
                    ca = fs.readFileSync(new URL('../../../ca.pem', import.meta.url));
                } catch (e2) {
                    throw new Error('ca.pem not found');
                }
            }
            
            const testConnection = await mysql.createConnection({
                host: process.env.DB_HOST,
                port: process.env.DB_PORT,
                user: dbUsername,
                password: dbPassword,
                database: process.env.DB_NAME,
                ssl: { ca }
            });
            
            // Test query
            const [rows] = await testConnection.execute('SELECT 1 + 1 AS solution');
            
            await testConnection.end();
            
            return {
                success: true,
                message: `Database user connection test successful`,
                testResult: rows
            };
            
        } catch (error) {
            console.error(`❌ Database user connection test failed:`, error);
            // Try fallback connection
            try {
                const adminConnection = await connectToAivenDB('admin');
                const [rows] = await adminConnection.execute('SELECT 1 + 1 AS solution');
                adminConnection.release();
                
                return {
                    success: true,
                    message: `Database user connection test successful (using admin credentials)`,
                    testResult: rows
                };
            } catch (fallbackError) {
                return {
                    success: false,
                    message: `Database user connection test failed: ${error.message}`,
                    error: error.message
                };
            }
        }
    }
    
    /**
     * Get database role based on application role
     * @param {string} appRole - Application role
     * @returns {string} Database role
     */
    static getDatabaseRole(appRole) {
        const roleMapping = {
            'admin': 'admin_role',
            'manager': 'inventory_manager_role',
            'cashier': 'order_processor_role',
            'vendor': 'warehouse_staff_role',
            'barista': 'support_staff_role',
            'user': 'support_staff_role'
        };
        
        return roleMapping[appRole] || 'support_staff_role';
    }
    
    /**
     * Grant role-based permissions to a database user
     * @param {Object} connection - Database connection
     * @param {string} dbUsername - Database username
     * @param {string} role - Application role
     */
    static async grantRolePermissions(connection, dbUsername, role) {
        const dbName = process.env.DB_NAME;
        
        try {
            switch (role) {
                case 'admin':
                    // Admin gets full access
                    await connection.execute(
                        `GRANT ALL PRIVILEGES ON ${dbName}.* TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT CREATE USER ON *.* TO '${dbUsername}'@'%'`
                    );
                    break;
                    
                case 'manager':
                    // Manager gets full access to application tables
                    await connection.execute(
                        `GRANT SELECT, INSERT, UPDATE, DELETE ON ${dbName}.* TO '${dbUsername}'@'%'`
                    );
                    break;
                    
                case 'cashier':
                    // Cashier gets access to orders, customers, payments
                    await connection.execute(
                        `GRANT SELECT, INSERT, UPDATE ON ${dbName}.customers TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT, INSERT, UPDATE ON ${dbName}.orders TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT, INSERT, UPDATE ON ${dbName}.order_items TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT, INSERT, UPDATE ON ${dbName}.payments TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT ON ${dbName}.products TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT, UPDATE ON ${dbName}.loyalty_points TO '${dbUsername}'@'%'`
                    );
                    break;
                    
                case 'vendor':
                    // Vendor gets access to suppliers, materials, inventory
                    await connection.execute(
                        `GRANT SELECT, INSERT, UPDATE ON ${dbName}.suppliers TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT, INSERT, UPDATE ON ${dbName}.materials TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT, INSERT, UPDATE ON ${dbName}.inventory_logs TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT ON ${dbName}.products TO '${dbUsername}'@'%'`
                    );
                    break;
                    
                case 'barista':
                case 'user':
                    // Barista/User gets read-only access
                    await connection.execute(
                        `GRANT SELECT ON ${dbName}.customers TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT ON ${dbName}.orders TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT ON ${dbName}.order_items TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT ON ${dbName}.products TO '${dbUsername}'@'%'`
                    );
                    await connection.execute(
                        `GRANT SELECT ON ${dbName}.loyalty_points TO '${dbUsername}'@'%'`
                    );
                    break;
                    
                default:
                    throw new Error(`Unknown role: ${role}`);
            }
            
            console.log(`✅ Permissions granted for role: ${role}`);
            
        } catch (error) {
            console.error(`❌ Error granting permissions for role ${role}:`, error);
            throw error;
        }
    }
    
    /**
     * List all application-created database users
     * @returns {Promise<Object>} Result object with user list
     */
    static async listDatabaseUsers() {
        try {
            const adminConnection = await connectToAivenDB('admin');
            
            const [rows] = await adminConnection.execute(
                `SELECT User, Host FROM mysql.user WHERE User LIKE 'app_%'`
            );
            
            adminConnection.release();
            
            return {
                success: true,
                message: 'Database users retrieved successfully',
                users: rows
            };
            
        } catch (error) {
            console.error(`❌ Error listing database users:`, error);
            return {
                success: false,
                message: `Failed to list database users: ${error.message}`,
                error: error.message
            };
        }
    }
}

export default DatabaseUserManager;
