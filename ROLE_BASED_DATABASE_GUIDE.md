# 🔐 Role-Based Database Authentication System

## Overview
This system implements **role-based database authentication** where each user login corresponds to a specific database user with appropriate permissions. When a user logs in successfully, the database connection uses that user's database credentials rather than a single admin connection.

## 🏗️ System Architecture

### 1. **Dual User System**
- **Application Users**: Stored in `.env` file for application authentication
- **Database Users**: Separate database users with role-based permissions

### 2. **Authentication Flow**
```
User Login (app credentials) → Role Validation → Database Connection (db credentials) → Access Control
```

## 📋 User Role Structure

### Application Roles & Database Users

| App Role | Database User | Database Role | Permissions |
|----------|---------------|---------------|-------------|
| `admin` | `admin_user` | `admin_role` | Full database access |
| `manager` | `manager_user` | `inventory_manager_role` | Employee, inventory, reports |
| `cashier` | `cashier_user` | `order_processor_role` | Orders, payments, products |
| `vendor` | `vendor_user` | `warehouse_staff_role` | Suppliers, materials |
| `barista` | `barista_user` | `support_staff_role` | Products, orders (read) |
| `user` | `regular_user` | `support_staff_role` | Customer data, orders |

## 🛠️ Configuration

### Environment Variables (`.env`)
```env
# Database Connection
DB_HOST=coffe-management-db1st-choengrayu307-607c.b.aivencloud.com
DB_PORT=21011
DB_NAME=coffee_management_db

# Role-Based Database Users
DB_ADMIN_USER=admin_user
DB_ADMIN_PASSWORD=admin123_db
DB_MANAGER_USER=manager_user
DB_MANAGER_PASSWORD=manager123_db
DB_CASHIER_USER=cashier_user
DB_CASHIER_PASSWORD=cashier123_db
DB_VENDOR_USER=vendor_user
DB_VENDOR_PASSWORD=vendor123_db
DB_BARISTA_USER=barista_user
DB_BARISTA_PASSWORD=barista123_db
DB_USER_USER=regular_user
DB_USER_PASSWORD=user123_db

# Application Users
USER_AND_PASSWORD_AND_HOST_1=manager,manager123,localhost,manager
USER_AND_PASSWORD_AND_HOST_2=cashier,cashier123,localhost,cashier
USER_AND_PASSWORD_AND_HOST_3=user,user123,localhost,user
```

## 🔧 Implementation Components

### 1. **RoleBasedDBConnection.js**
- **Purpose**: Manages database connections per role
- **Features**:
  - Connection pooling per role
  - SSL certificate handling
  - Connection testing
  - Graceful shutdown

```javascript
// Usage
import { connectToAivenDB } from './RoleBasedDBConnection.js';

// Get connection for specific role
const connection = await connectToAivenDB('manager');
```

### 2. **AuthService.js** (Updated)
- **Purpose**: Handles authentication with database validation
- **Features**:
  - Role-based authentication
  - Database connection testing
  - Session management with DB role info

```javascript
// Authentication now includes DB connection test
const user = await AuthService.authenticate(username, password);
// Returns: { username, role, token, dbRole }
```

### 3. **roleBasedAuthMiddleware.js**
- **Purpose**: Middleware for role-based access control
- **Features**:
  - Token authentication
  - Role validation
  - Database connection injection
  - Automatic connection cleanup

```javascript
// Usage in routes
app.get('/api/data', authenticateToken, withRoleBasedDB, (req, res) => {
    // req.dbConnection is available with user's role permissions
    // req.user contains user info
});
```

## 🔒 Security Features

### 1. **Database-Level Security**
- Each role has specific database permissions
- Connection pooling prevents connection exhaustion
- SSL certificates for secure connections

### 2. **Application-Level Security**
- Token-based authentication
- Role-based middleware
- Connection cleanup and resource management

### 3. **Error Handling**
- Database connection failure detection
- Graceful degradation
- Audit logging

## 📊 Connection Testing

### Startup Tests
Server tests all role connections on startup:
```
🔍 Testing Role-Based Database Connections...

Testing admin connection...
✅ admin database connection successful
Testing manager connection...
✅ manager database connection successful
Testing cashier connection...
✅ cashier database connection successful
...

📊 Database Connection Summary: 6/6 roles connected successfully
```

### Runtime Tests
```javascript
// Test specific role connection
const success = await testRoleConnection('manager');
```

## 🚀 Usage Examples

### 1. **Login Process**
```javascript
// Frontend login
const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'manager', password: 'manager123' })
});

// Response includes database role info
const data = await response.json();
// { username: 'manager', role: 'manager', token: '...', dbRole: 'manager' }
```

### 2. **API Endpoint with Role-Based DB**
```javascript
// Route with role-based database access
app.get('/api/orders', authenticateToken, withRoleBasedDB, async (req, res) => {
    try {
        // req.dbConnection uses the user's role database permissions
        const [orders] = await req.dbConnection.execute('SELECT * FROM orders');
        res.json({ success: true, data: orders });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});
```

### 3. **Manual Database Connection**
```javascript
// Get connection for specific user session
const connection = await AuthService.getDatabaseConnection(userToken);
const [results] = await connection.execute('SELECT * FROM allowed_table');
connection.release();
```

## 🔄 Migration from Single Connection

### Before (Single Admin Connection)
```javascript
// Everyone uses the same database connection
const connection = await connectToAivenDB();
```

### After (Role-Based Connections)
```javascript
// Each user gets their own role-based connection
const connection = await connectToAivenDB(user.role);
```

## 📈 Benefits

1. **Security**: Database-level access control
2. **Scalability**: Connection pooling per role
3. **Audit**: User actions tracked at database level
4. **Compliance**: Role-based permissions align with business rules
5. **Flexibility**: Easy to add new roles and permissions

## 🛡️ Database Setup Requirements

### 1. **Create Database Users**
```sql
-- Create database users for each role
CREATE USER 'admin_user'@'%' IDENTIFIED BY 'admin123_db';
CREATE USER 'manager_user'@'%' IDENTIFIED BY 'manager123_db';
CREATE USER 'cashier_user'@'%' IDENTIFIED BY 'cashier123_db';
-- ... etc
```

### 2. **Grant Role-Based Permissions**
```sql
-- Grant roles to users
GRANT 'admin_role' TO 'admin_user'@'%';
GRANT 'inventory_manager_role' TO 'manager_user'@'%';
GRANT 'order_processor_role' TO 'cashier_user'@'%';
-- ... etc
```

### 3. **Set Default Roles**
```sql
-- Set default roles for each user
SET DEFAULT ROLE 'admin_role' TO 'admin_user'@'%';
SET DEFAULT ROLE 'inventory_manager_role' TO 'manager_user'@'%';
-- ... etc
```

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database user credentials in `.env`
   - Verify database user exists and has correct permissions
   - Test SSL certificate path

2. **Role Not Recognized**
   - Verify role exists in `AVAILABLE_ROLES`
   - Check database user mapping
   - Confirm role permissions

3. **Token Invalid**
   - Check token expiration
   - Verify session storage
   - Confirm authentication middleware

### Debug Commands
```javascript
// Test all role connections
await testRoleBasedConnections();

// Test specific role
await testRoleConnection('manager');

// Check current sessions
console.log(AuthService.getActiveSessions());
```

This system provides enterprise-level security by implementing true role-based database access control, ensuring that users can only access data and perform operations that their role permits at the database level.
