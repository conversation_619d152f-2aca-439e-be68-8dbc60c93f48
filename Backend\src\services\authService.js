import EnvUserManager from '../utils/envUserManager.js';
import { connectToAivenDB, testRoleConnection } from '../config/RoleBasedDBConnection.js';

// Simple authentication service
console.log('AuthService initialized');

// Initialize environment user manager
const envUserManager = new EnvUserManager();

// In-memory session storage with database connection info
const sessions = new Map();

// Define available roles based on the database model
const AVAILABLE_ROLES = {
    'admin': 'Administrator (Full Access)',
    'manager': 'Manager (Employee Management)',
    'cashier': 'Cashier (Order Processing)',
    'vendor': 'Vendor (Supplier Management)',
    'barista': 'Barista (Product Preparation)',
    'user': 'User (Customer Access)'
};

/**
 * Authentication Service
 * Handles user authentication using .env file for credentials
 * and establishes role-based database connections
 */
class AuthService {
    /**
     * Get all users from .env file
     * @returns {Array} Array of user objects
     */
    static getUsers() {
        const users = envUserManager.parseUsersFromEnv();
        return users.map(user => ({
            username: user.username,
            role: user.role,
            name: user.name,
            source: user.source
        }));
    }

    /**
     * Get available roles
     * @returns {Object} Available roles with descriptions
     */
    static getAvailableRoles() {
        return AVAILABLE_ROLES;
    }

    /**
     * Authenticate user and establish database connection
     * @param {string} username - Username
     * @param {string} password - Password
     * @returns {Object|null} User object with session token or null if authentication fails
     */
    static async authenticate(username, password) {
        console.log(`🔐 Authentication attempt: ${username}`);

        try {
            // Get users from .env file
            const users = envUserManager.parseUsersFromEnv();

            // Find user by username
            const user = users.find(u => u.username === username);

            // Check if user exists and password matches
            if (!user || user.password !== password) {
                console.log(`❌ Authentication failed for: ${username}`);
                return null;
            }

            // Test database connection for the user's role
            const dbConnectionTest = await testRoleConnection(user.role);
            if (!dbConnectionTest) {
                console.log(`❌ Database connection failed for role: ${user.role}`);
                return {
                    error: 'Database connection failed for your role. Please contact administrator.',
                    username: user.username,
                    role: user.role
                };
            }

            console.log(`✅ Authentication successful for: ${username} (${user.role})`);

            // Generate simple session token
            const sessionToken = Math.random().toString(36).substring(2, 15);

            // Store session with database connection info
            sessions.set(sessionToken, {
                username: user.username,
                role: user.role,
                name: user.name,
                loginTime: new Date(),
                dbConnectionRole: user.role // Store the database role for connection
            });

            return {
                username: user.username,
                role: user.role,
                name: user.name,
                token: sessionToken,
                dbRole: user.role // Include database role in response
            };
        } catch (error) {
            console.error(`❌ Authentication error for ${username}:`, error);
            return null;
        }
    }

    /**
     * Get database connection for authenticated user
     * @param {string} token - Session token
     * @returns {Promise<Connection>} Database connection for user's role
     */
    static async getDatabaseConnection(token) {
        const session = sessions.get(token);
        if (!session) {
            throw new Error('Invalid session token');
        }

        return await connectToAivenDB(session.dbConnectionRole);
    }
    
    /**
     * Authenticate user and establish database connection
     * @param {string} username - Username
     * @param {string} password - Password
     * @returns {Object|null} User object with session token or null if authentication fails
     */
    static async authenticate(username, password) {
        console.log(`🔐 Authentication attempt: ${username}`);

        try {
            // Get users from .env file
            const users = envUserManager.parseUsersFromEnv();

            // Find user by username
            const user = users.find(u => u.username === username);

            // Check if user exists and password matches
            if (!user || user.password !== password) {
                console.log(`❌ Authentication failed for: ${username}`);
                return null;
            }

            // Test database connection for the user's role
            const dbConnectionTest = await testRoleConnection(user.role);
            if (!dbConnectionTest) {
                console.log(`❌ Database connection failed for role: ${user.role}`);
                return {
                    error: 'Database connection failed for your role. Please contact administrator.',
                    username: user.username,
                    role: user.role
                };
            }

            console.log(`✅ Authentication successful for: ${username} (${user.role})`);

            // Generate simple session token
            const sessionToken = Math.random().toString(36).substring(2, 15);

            // Store session with database connection info
            sessions.set(sessionToken, {
                username: user.username,
                role: user.role,
                name: user.name,
                loginTime: new Date(),
                dbConnectionRole: user.role // Store the database role for connection
            });

            return {
                username: user.username,
                role: user.role,
                name: user.name,
                token: sessionToken,
                dbRole: user.role // Include database role in response
            };
        } catch (error) {
            console.error(`❌ Authentication error for ${username}:`, error);
            return null;
        }
    }

    /**
     * Get database connection for authenticated user
     * @param {string} token - Session token
     * @returns {Promise<Connection>} Database connection for user's role
     */
    static async getDatabaseConnection(token) {
        const session = sessions.get(token);
        if (!session) {
            throw new Error('Invalid session token');
        }

        return await connectToAivenDB(session.dbConnectionRole);
    }
    
    /**
     * Verify session token
     * @param {string} token - Session token
     * @returns {Object|null} Session data or null if invalid
     */
    static verifyToken(token) {
        return sessions.get(token) || null;
    }

    /**
     * Logout user (invalidate session)
     * @param {string} token - Session token
     * @returns {boolean} Success status
     */
    static logout(token) {
        if (sessions.has(token)) {
            sessions.delete(token);
            return true;
        }
        return false;
    }
    
    /**
     * Create a new user and store in .env file and database
     * @param {Object} userData - User data
     * @param {string} requestingUser - Username of the user making the request
     * @returns {Object} Result object with success status and message
     */
    static async createUser(userData, requestingUser = null) {
        try {
            const { username, password, role = 'user', name } = userData;

            // Validate that only admin can create users
            if (requestingUser) {
                const users = envUserManager.parseUsersFromEnv();
                const requester = users.find(u => u.username === requestingUser);
                if (!requester || requester.role !== 'admin') {
                    return {
                        success: false,
                        message: 'Only administrators can create users'
                    };
                }
            }

            // Validate required fields
            if (!username || !password) {
                return {
                    success: false,
                    message: 'Username and password are required'
                };
            }

            // Validate role
            if (!Object.keys(AVAILABLE_ROLES).includes(role)) {
                return {
                    success: false,
                    message: `Invalid role. Available roles: ${Object.keys(AVAILABLE_ROLES).join(', ')}`
                };
            }

            // Check username length and format
            if (username.length < 3 || username.length > 20) {
                return {
                    success: false,
                    message: 'Username must be between 3 and 20 characters'
                };
            }

            // Check password strength
            if (password.length < 6) {
                return {
                    success: false,
                    message: 'Password must be at least 6 characters long'
                };
            }

            // Try to add user to .env file and database
            const result = await envUserManager.addUser({
                username,
                password,
                role,
                name: name || username
            });

            return result;
        } catch (error) {
            console.error('❌ Error creating user:', error);
            return {
                success: false,
                message: 'An error occurred while creating the user',
                error: error.message
            };
        }
    }

    /**
     * Update user in .env file
     * @param {string} username - Username to update
     * @param {Object} userData - Updated user data
     * @param {string} requestingUser - Username of the user making the request
     * @returns {Object} Result object with success status and message
     */
    static async updateUser(username, userData, requestingUser = null) {
        try {
            // Validate that only admin can update users
            if (requestingUser) {
                const users = envUserManager.parseUsersFromEnv();
                const requester = users.find(u => u.username === requestingUser);
                if (!requester || requester.role !== 'admin') {
                    return {
                        success: false,
                        message: 'Only administrators can update users'
                    };
                }
            }

            // Validate role if provided
            if (userData.role && !Object.keys(AVAILABLE_ROLES).includes(userData.role)) {
                return {
                    success: false,
                    message: `Invalid role. Available roles: ${Object.keys(AVAILABLE_ROLES).join(', ')}`
                };
            }

            // Try to update user in .env file and database
            const result = await envUserManager.updateUser(username, userData);

            return result;
        } catch (error) {
            console.error('❌ Error updating user:', error);
            return {
                success: false,
                message: 'An error occurred while updating the user',
                error: error.message
            };
        }
    }

    /**
     * Delete user from .env file
     * @param {string} username - Username to delete
     * @param {string} requestingUser - Username of the user making the request
     * @returns {Object} Result object with success status and message
     */
    static async deleteUser(username, requestingUser = null) {
        try {
            // Validate that only admin can delete users
            if (requestingUser) {
                const users = envUserManager.parseUsersFromEnv();
                const requester = users.find(u => u.username === requestingUser);
                if (!requester || requester.role !== 'admin') {
                    return {
                        success: false,
                        message: 'Only administrators can delete users'
                    };
                }
            }

            // Try to delete user from .env file and database
            const result = await envUserManager.deleteUser(username);

            return result;
        } catch (error) {
            console.error('❌ Error deleting user:', error);
            return {
                success: false,
                message: 'An error occurred while deleting the user',
                error: error.message
            };
        }
    }
}

export default AuthService;
