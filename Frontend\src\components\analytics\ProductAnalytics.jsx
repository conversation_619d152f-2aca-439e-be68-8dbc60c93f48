import React, { useState, useEffect } from 'react';
import { api } from '../../services/api.js';

const ProductAnalytics = () => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [productData, setProductData] = useState({
        revenueByProduct: [],
        mostSoldProduct: null,
        mostFrequentProduct: null,
        unusedProducts: [],
        rawMaterialCosts: [],
        zeroQuantityProducts: []
    });

    useEffect(() => {
        fetchProductData();
    }, []);

    const fetchProductData = async () => {
        try {
            setLoading(true);
            const [
                revenueRes,
                mostSoldRes,
                mostFrequentRes,
                unusedRes,
                rawMaterialRes,
                zeroQuantityRes
            ] = await Promise.all([
                api.get('/analytics/revenue/by-product'),
                api.get('/analytics/products/most-sold'),
                api.get('/analytics/products/most-frequent'),
                api.get('/analytics/products/unused'),
                api.get('/analytics/products/raw-material-cost'),
                api.get('/analytics/zero-quantity-products')
            ]);

            setProductData({
                revenueByProduct: revenueRes.data.data || [],
                mostSoldProduct: mostSoldRes.data.data,
                mostFrequentProduct: mostFrequentRes.data.data,
                unusedProducts: unusedRes.data.data || [],
                rawMaterialCosts: rawMaterialRes.data.data || [],
                zeroQuantityProducts: zeroQuantityRes.data.data || []
            });
        } catch (error) {
            console.error('Error fetching product data:', error);
            setError('Failed to load product analytics');
        } finally {
            setLoading(false);
        }
    };

    const StatCard = ({ title, value, subtitle, icon, color = 'blue' }) => (
        <div className={`bg-white rounded-lg shadow-md p-6 border-l-4 border-${color}-500`}>
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-2xl font-bold text-gray-900">{value}</p>
                    {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
                </div>
                <div className="text-3xl">{icon}</div>
            </div>
        </div>
    );

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                    <div className="text-red-500">⚠️</div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="text-sm text-red-700">{error}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                    title="Top Revenue Product"
                    value={productData.revenueByProduct[0]?.product_name || 'N/A'}
                    subtitle={productData.revenueByProduct[0] ? `$${parseFloat(productData.revenueByProduct[0].total_revenue).toLocaleString()}` : ''}
                    icon="💰"
                    color="green"
                />
                <StatCard
                    title="Most Sold Product"
                    value={productData.mostSoldProduct?.product_name || 'N/A'}
                    subtitle={productData.mostSoldProduct ? `${productData.mostSoldProduct.total_quantity_sold} units` : ''}
                    icon="🏆"
                    color="yellow"
                />
                <StatCard
                    title="Unused Products"
                    value={productData.unusedProducts.length}
                    subtitle="Never ordered"
                    icon="📦"
                    color="red"
                />
                <StatCard
                    title="Low Stock Issues"
                    value={productData.zeroQuantityProducts.length}
                    subtitle="Zero quantity materials"
                    icon="⚠️"
                    color="orange"
                />
            </div>

            {/* Revenue by Product */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Revenue by Product</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Product
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Category
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Revenue
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Quantity Sold
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Avg Price
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {productData.revenueByProduct.slice(0, 10).map((product, index) => (
                                <tr key={index} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900">
                                            {product.product_name}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                            {product.category_name}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${parseFloat(product.total_revenue).toLocaleString()}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {product.total_quantity_sold}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${(parseFloat(product.total_revenue) / parseInt(product.total_quantity_sold)).toFixed(2)}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Raw Material Costs */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Raw Material Costs per Product</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Product
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Raw Material Cost
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Cost Level
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {productData.rawMaterialCosts.slice(0, 10).map((product, index) => {
                                const cost = parseFloat(product.raw_material_cost);
                                const costLevel = cost > 10 ? 'High' : cost > 5 ? 'Medium' : 'Low';
                                const costColor = cost > 10 ? 'red' : cost > 5 ? 'yellow' : 'green';
                                
                                return (
                                    <tr key={index} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                {product.product_name}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${cost.toFixed(2)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`px-2 py-1 text-xs font-medium bg-${costColor}-100 text-${costColor}-800 rounded-full`}>
                                                {costLevel}
                                            </span>
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Problem Products */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Unused Products */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold mb-4 text-red-600">Unused Products</h3>
                    <div className="space-y-3">
                        {productData.unusedProducts.length > 0 ? (
                            productData.unusedProducts.slice(0, 5).map((product, index) => (
                                <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                    <div>
                                        <div className="font-medium text-red-900">{product.product_name}</div>
                                        <div className="text-sm text-red-600">{product.category_name}</div>
                                    </div>
                                    <div className="text-red-700">${parseFloat(product.unit_price).toFixed(2)}</div>
                                </div>
                            ))
                        ) : (
                            <div className="text-center text-gray-500 py-4">All products have been ordered</div>
                        )}
                    </div>
                </div>

                {/* Zero Quantity Products */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold mb-4 text-orange-600">Low Stock Materials</h3>
                    <div className="space-y-3">
                        {productData.zeroQuantityProducts.length > 0 ? (
                            productData.zeroQuantityProducts.slice(0, 5).map((product, index) => (
                                <div key={index} className="p-3 bg-orange-50 rounded-lg">
                                    <div className="font-medium text-orange-900">{product.product_name}</div>
                                    <div className="text-sm text-orange-600">
                                        Eating: {product.material_eating} ({product.eating_quantity} units)
                                    </div>
                                    <div className="text-sm text-orange-600">
                                        Object: {product.material_object} ({product.object_quantity} units)
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="text-center text-gray-500 py-4">All materials are in stock</div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProductAnalytics;
