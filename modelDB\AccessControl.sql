
-- Grant permissions to Admin role
GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.customers TO 'admin_role';
GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.employees TO 'admin_role';
GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.orders TO 'admin_role';
GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.payments TO 'admin_role';
GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.products TO 'admin_role';
GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.suppliers TO 'admin_role';

-- Grant permissions to Order Processor role
GRANT SELECT, UPDATE ON coffee_management_db.orders TO 'order_processor_role';
GRANT SELECT, UPDATE ON coffee_management_db.products TO 'order_processor_role';
GRANT SELECT ON coffee_management_db.material_object TO 'order_processor_role';

-- Grant permissions to Warehouse Staff role
GRANT SELECT, UPDATE ON coffee_management_db.material_eating TO 'warehouse_staff_role';
GRANT SELECT, UPDATE ON coffee_management_db.material_object TO 'warehouse_staff_role';
GRANT SELECT ON coffee_management_db.products TO 'warehouse_staff_role';

-- Grant permissions to Inventory Manager role
GRANT SELECT, INSERT, UPDATE ON coffee_management_db.material_eating TO 'inventory_manager_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management_db.material_object TO 'inventory_manager_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management_db.products TO 'inventory_manager_role';
GRANT SELECT, INSERT, UPDATE ON coffee_management_db.suppliers TO 'inventory_manager_role';

-- Grant permissions to Finance Staff role
GRANT SELECT, INSERT, UPDATE ON coffee_management_db.payments TO 'finance_staff_role';
GRANT SELECT ON coffee_management_db.orders TO 'finance_staff_role';
GRANT SELECT ON coffee_management_db.customers TO 'finance_staff_role';

-- Grant permissions to Support Staff role
GRANT SELECT, UPDATE ON coffee_management_db.customers TO 'support_staff_role';
GRANT SELECT ON coffee_management_db.orders TO 'support_staff_role';

-- Grant permissions to Super Admin role
GRANT ALL PRIVILEGES ON coffee_management_db.* TO 'super_admin_role' WITH GRANT OPTION;


-- Assign roles to users
GRANT 'admin_role' TO 'admin_user'@'localhost';
GRANT 'order_processor_role' TO 'order_processor_user'@'localhost';
GRANT 'warehouse_staff_role' TO 'warehouse_staff_user'@'localhost';
GRANT 'inventory_manager_role' TO 'inventory_manager_user'@'localhost';
GRANT 'finance_staff_role' TO 'finance_staff_user'@'localhost';
GRANT 'support_staff_role' TO 'support_staff_user'@'localhost';
GRANT 'super_admin_role' TO 'super_admin_user'@'localhost';

-- Set default roles (for MySQL 8.0+)
SET DEFAULT ROLE ALL TO 'admin_user'@'localhost';
SET DEFAULT ROLE ALL TO 'order_processor_user'@'localhost';
SET DEFAULT ROLE ALL TO 'warehouse_staff_user'@'localhost';
SET DEFAULT ROLE ALL TO 'inventory_manager_user'@'localhost';
SET DEFAULT ROLE ALL TO 'finance_staff_user'@'localhost';
SET DEFAULT ROLE ALL TO 'support_staff_user'@'localhost';
SET DEFAULT ROLE ALL TO 'super_admin_user'@'localhost';

-- Flush privileges to apply changes
FLUSH PRIVILEGES;