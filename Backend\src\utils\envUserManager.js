import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import DatabaseUserManager from './databaseUserManager.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Environment File Manager
 * Handles reading and writing user data to .env file
 */
class EnvUserManager {
    constructor() {
        this.envPath = path.join(__dirname, '../../.env');
    }

    /**
     * Read .env file content
     * @returns {string} File content
     */
    readEnvFile() {
        try {
            return fs.readFileSync(this.envPath, 'utf8');
        } catch (error) {
            console.error('Error reading .env file:', error);
            return '';
        }
    }

    /**
     * Write content to .env file
     * @param {string} content - Content to write
     */
    writeEnvFile(content) {
        try {
            fs.writeFileSync(this.envPath, content, 'utf8');
        } catch (error) {
            console.error('Error writing .env file:', error);
            throw error;
        }
    }

    /**
     * Parse users from .env file
     * @returns {Array} Array of user objects
     */
    parseUsersFromEnv() {
        const content = this.readEnvFile();
        const lines = content.split('\n');
        const users = [];

        // Default admin user (hardcoded for security)
        users.push({
            username: 'admin',
            password: 'admin123',
            role: 'admin',
            name: 'Administrator',
            source: 'hardcoded'
        });

        lines.forEach(line => {
            if (line.startsWith('USER_AND_PASSWORD_AND_HOST_')) {
                const match = line.match(/USER_AND_PASSWORD_AND_HOST_\d+=(.+)/);
                if (match) {
                    const userDataStr = match[1].split(',');
                    if (userDataStr.length >= 4) {
                        const [username, password, host, role] = userDataStr;
                        users.push({
                            username: username.trim(),
                            password: password.trim(),
                            role: role.trim(),
                            name: username.trim(),
                            source: 'env'
                        });
                    }
                }
            }
        });

        return users;
    }

    /**
     * Get next available user number
     * @returns {number} Next available user number
     */
    getNextUserNumber() {
        const content = this.readEnvFile();
        const lines = content.split('\n');
        let maxNum = 0;

        lines.forEach(line => {
            if (line.startsWith('USER_AND_PASSWORD_AND_HOST_')) {
                const match = line.match(/USER_AND_PASSWORD_AND_HOST_(\d+)=/);
                if (match) {
                    const num = parseInt(match[1]);
                    if (num > maxNum) {
                        maxNum = num;
                    }
                }
            }
        });

        return maxNum + 1;
    }

    /**
     * Add a new user to .env file and create database user
     * @param {Object} userData - User data to add
     * @returns {Object} Result object with success status and messages
     */
    async addUser(userData) {
        try {
            const { username, password, role = 'user' } = userData;
            
            // Check if user already exists
            const existingUsers = this.parseUsersFromEnv();
            if (existingUsers.find(u => u.username === username)) {
                throw new Error('User already exists');
            }

            console.log(`🔄 Creating new user: ${username} with role: ${role}`);
            
            // Step 1: Create database user first
            const dbResult = await DatabaseUserManager.createDatabaseUser(userData);
            if (!dbResult.success) {
                throw new Error(`Database user creation failed: ${dbResult.message}`);
            }

            // Step 2: Add to .env file
            const content = this.readEnvFile();
            const nextNum = this.getNextUserNumber();
            const newUserLine = `USER_AND_PASSWORD_AND_HOST_${nextNum}=${username},${password},localhost,${role}`;
            
            // Add the new user line to the end of the file
            const newContent = content + '\n' + newUserLine;
            this.writeEnvFile(newContent);

            console.log(`✅ User created successfully: ${username}`);
            
            return {
                success: true,
                message: `User ${username} created successfully in both .env file and database`,
                envUser: true,
                dbUser: dbResult.success,
                dbUsername: dbResult.dbUsername,
                dbRole: dbResult.dbRole
            };
            
        } catch (error) {
            console.error('❌ Error adding user:', error);
            return {
                success: false,
                message: error.message,
                error: error.message
            };
        }
    }

    /**
     * Update an existing user in .env file and database
     * @param {string} username - Username to update
     * @param {Object} userData - Updated user data
     * @returns {Object} Result object with success status and messages
     */
    async updateUser(username, userData) {
        try {
            // Cannot update admin user
            if (username === 'admin') {
                throw new Error('Cannot update admin user');
            }

            console.log(`🔄 Updating user: ${username}`);

            // Get current user data
            const existingUsers = this.parseUsersFromEnv();
            const currentUser = existingUsers.find(u => u.username === username);
            
            if (!currentUser) {
                throw new Error('User not found');
            }

            const content = this.readEnvFile();
            const lines = content.split('\n');
            let updated = false;
            let dbUpdateResults = {};

            const updatedLines = lines.map(line => {
                if (line.startsWith('USER_AND_PASSWORD_AND_HOST_')) {
                    const match = line.match(/USER_AND_PASSWORD_AND_HOST_(\d+)=(.+)/);
                    if (match) {
                        const userDataStr = match[2].split(',');
                        if (userDataStr.length >= 4 && userDataStr[0].trim() === username) {
                            // Update user data
                            const newPassword = userData.password || userDataStr[1].trim();
                            const newRole = userData.role || userDataStr[3].trim();
                            updated = true;
                            return `USER_AND_PASSWORD_AND_HOST_${match[1]}=${username},${newPassword},localhost,${newRole}`;
                        }
                    }
                }
                return line;
            });

            if (updated) {
                // Update database user if password changed
                if (userData.password) {
                    const passwordResult = await DatabaseUserManager.updateDatabaseUserPassword(username, userData.password);
                    dbUpdateResults.passwordUpdate = passwordResult;
                }

                // Update database user role if role changed
                if (userData.role && userData.role !== currentUser.role) {
                    const roleResult = await DatabaseUserManager.updateDatabaseUserRole(username, userData.role, currentUser.role);
                    dbUpdateResults.roleUpdate = roleResult;
                }

                // Update .env file
                this.writeEnvFile(updatedLines.join('\n'));

                console.log(`✅ User updated successfully: ${username}`);

                return {
                    success: true,
                    message: `User ${username} updated successfully in both .env file and database`,
                    envUpdate: true,
                    dbUpdates: dbUpdateResults
                };
            }

            return {
                success: false,
                message: 'User not found in .env file'
            };
        } catch (error) {
            console.error('❌ Error updating user:', error);
            return {
                success: false,
                message: error.message,
                error: error.message
            };
        }
    }

    /**
     * Delete a user from .env file and database
     * @param {string} username - Username to delete
     * @returns {Object} Result object with success status and messages
     */
    async deleteUser(username) {
        try {
            // Cannot delete admin user
            if (username === 'admin') {
                throw new Error('Cannot delete admin user');
            }

            console.log(`🔄 Deleting user: ${username}`);

            // Step 1: Delete from database
            const dbResult = await DatabaseUserManager.deleteDatabaseUser(username);

            // Step 2: Delete from .env file
            const content = this.readEnvFile();
            const lines = content.split('\n');
            let deleted = false;

            const filteredLines = lines.filter(line => {
                if (line.startsWith('USER_AND_PASSWORD_AND_HOST_')) {
                    const match = line.match(/USER_AND_PASSWORD_AND_HOST_\d+=(.+)/);
                    if (match) {
                        const userDataStr = match[1].split(',');
                        if (userDataStr.length >= 4 && userDataStr[0].trim() === username) {
                            deleted = true;
                            return false; // Filter out this line
                        }
                    }
                }
                return true; // Keep all other lines
            });

            if (deleted) {
                this.writeEnvFile(filteredLines.join('\n'));
                
                console.log(`✅ User deleted successfully: ${username}`);
                
                return {
                    success: true,
                    message: `User ${username} deleted successfully from both .env file and database`,
                    envDelete: true,
                    dbDelete: dbResult.success,
                    dbMessage: dbResult.message
                };
            }

            return {
                success: false,
                message: 'User not found in .env file'
            };
        } catch (error) {
            console.error('❌ Error deleting user:', error);
            return {
                success: false,
                message: error.message,
                error: error.message
            };
        }
    }
}

export default EnvUserManager;
