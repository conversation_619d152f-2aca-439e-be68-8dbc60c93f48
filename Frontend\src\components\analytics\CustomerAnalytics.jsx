import React, { useState, useEffect } from 'react';
import { api } from '../../services/api.js';

const CustomerAnalytics = () => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [customerData, setCustomerData] = useState({
        topCustomers: [],
        loyaltyPoints: [],
        orderHistory: [],
        selectedCustomer: null
    });

    useEffect(() => {
        fetchCustomerData();
    }, []);

    const fetchCustomerData = async () => {
        try {
            setLoading(true);
            const [
                topCustomersRes,
                loyaltyPointsRes,
                orderHistoryRes
            ] = await Promise.all([
                api.get('/analytics/customers/top-spenders?limit=10'),
                api.get('/analytics/loyalty-points'),
                api.get('/analytics/customers/order-history')
            ]);

            setCustomerData({
                topCustomers: topCustomersRes.data.data || [],
                loyaltyPoints: loyaltyPointsRes.data.data || [],
                orderHistory: orderHistoryRes.data.data || [],
                selectedCustomer: null
            });
        } catch (error) {
            console.error('Error fetching customer data:', error);
            setError('Failed to load customer analytics');
        } finally {
            setLoading(false);
        }
    };

    const fetchCustomerOrderHistory = async (customerId) => {
        try {
            const response = await api.get(`/analytics/customers/order-history?customer_id=${customerId}`);
            setCustomerData(prev => ({
                ...prev,
                selectedCustomer: customerId,
                orderHistory: response.data.data || []
            }));
        } catch (error) {
            console.error('Error fetching customer order history:', error);
        }
    };

    const StatCard = ({ title, value, subtitle, icon, color = 'blue' }) => (
        <div className={`bg-white rounded-lg shadow-md p-6 border-l-4 border-${color}-500`}>
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-2xl font-bold text-gray-900">{value}</p>
                    {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
                </div>
                <div className="text-3xl">{icon}</div>
            </div>
        </div>
    );

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                    <div className="text-red-500">⚠️</div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="text-sm text-red-700">{error}</div>
                    </div>
                </div>
            </div>
        );
    }

    const totalCustomers = customerData.topCustomers.length;
    const totalRevenue = customerData.topCustomers.reduce((sum, customer) => sum + parseFloat(customer.total_spent), 0);
    const totalOrders = customerData.topCustomers.reduce((sum, customer) => sum + parseInt(customer.total_orders), 0);
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    return (
        <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                    title="Total Customers"
                    value={totalCustomers}
                    subtitle="Active customers"
                    icon="👥"
                    color="blue"
                />
                <StatCard
                    title="Total Revenue"
                    value={`$${totalRevenue.toLocaleString()}`}
                    subtitle="From all customers"
                    icon="💰"
                    color="green"
                />
                <StatCard
                    title="Total Orders"
                    value={totalOrders.toLocaleString()}
                    subtitle="All time"
                    icon="📋"
                    color="purple"
                />
                <StatCard
                    title="Avg Order Value"
                    value={`$${avgOrderValue.toFixed(2)}`}
                    subtitle="Per order"
                    icon="📊"
                    color="yellow"
                />
            </div>

            {/* Top Customers */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Top Customers by Spending</h3>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Rank
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Customer
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Email
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Spent
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Orders
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Avg Order
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {customerData.topCustomers.map((customer, index) => (
                                <tr key={customer.customer_id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                                            {index + 1}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900">
                                            {customer.customer_name}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {customer.email}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${parseFloat(customer.total_spent).toLocaleString()}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {customer.total_orders}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${(parseFloat(customer.total_spent) / parseInt(customer.total_orders)).toFixed(2)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button
                                            onClick={() => fetchCustomerOrderHistory(customer.customer_id)}
                                            className="text-blue-600 hover:text-blue-900"
                                        >
                                            View History
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Loyalty Points */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Customer Loyalty Points</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {customerData.loyaltyPoints.slice(0, 9).map((customer, index) => (
                        <div key={customer.customer_id} className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="font-medium text-gray-900">{customer.customer_name}</div>
                                    <div className="text-sm text-gray-600">Customer ID: {customer.customer_id}</div>
                                </div>
                                <div className="text-right">
                                    <div className="text-2xl font-bold text-purple-600">{customer.total_points}</div>
                                    <div className="text-sm text-purple-500">points</div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Customer Order History */}
            {customerData.selectedCustomer && (
                <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">Order History - Customer #{customerData.selectedCustomer}</h3>
                        <button
                            onClick={() => setCustomerData(prev => ({ ...prev, selectedCustomer: null }))}
                            className="text-gray-500 hover:text-gray-700"
                        >
                            ✕
                        </button>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Order ID
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Product
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Quantity
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Unit Price
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {customerData.orderHistory.map((order, index) => (
                                    <tr key={index} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {order.order_id}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {order.product_name}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {order.quantity}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${parseFloat(order.unit_price).toFixed(2)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${order.total_amount ? parseFloat(order.total_amount).toFixed(2) : (parseFloat(order.unit_price) * parseInt(order.quantity)).toFixed(2)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {new Date(order.order_date).toLocaleDateString()}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CustomerAnalytics;
