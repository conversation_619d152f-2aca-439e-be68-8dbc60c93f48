# Dual Authentication System Setup Guide

## Overview

This system implements a dual authentication approach where each user login corresponds to both:
1. **Application Authentication** - User credentials stored in `.env` file
2. **Database Authentication** - Corresponding database user with role-based permissions

When an admin creates a user, the system automatically:
- ✅ Stores user in `.env` file for application login
- ✅ Creates database user with appropriate role permissions
- ✅ Tests database connectivity for the new user

## 🚀 Setup Instructions

### 1. Database Setup

First, run the SQL script to create roles and admin user:

```bash
# Connect to your Aiven MySQL database as root/admin
mysql -h <your-host> -P <port> -u <admin-user> -p --ssl-ca=ca.pem <database-name>

# Run the setup script
source Backend/sql/setup_database_roles.sql
```

### 2. Environment Configuration

Your `.env` file should contain:

```env
# Database connection (admin credentials for user creation)
DB_HOST=your-aiven-host
DB_PORT=3306
DB_USER=admin_user
DB_PASSWORD=admin_password
DB_NAME=coffee_management

# Role-based database credentials
DB_USER_ADMIN=app_admin
DB_PASSWORD_ADMIN=admin123_db

DB_USER_MANAGER=app_manager
DB_PASSWORD_MANAGER=manager123_db

DB_USER_CASHIER=app_cashier
DB_PASSWORD_CASHIER=cashier123_db

DB_USER_VENDOR=app_vendor
DB_PASSWORD_VENDOR=vendor123_db

DB_USER_BARISTA=app_barista
DB_PASSWORD_BARISTA=barista123_db

DB_USER_USER=app_user
DB_PASSWORD_USER=user123_db

# Default admin user for application
USER_AND_PASSWORD_AND_HOST_1=admin,admin123,localhost,admin
```

### 3. Start the Application

```bash
# Backend
cd Backend
npm install
npm start

# Frontend
cd Frontend
npm install
npm run dev
```

## 👤 User Management Workflow

### Creating a New User

1. **Login as Admin**
   - Username: `admin`
   - Password: `admin123`

2. **Navigate to User Management**
   - Go to Admin Dashboard
   - Click "User Management"

3. **Create New User**
   - Click "Create User"
   - Fill in details:
     - Username: `john_doe`
     - Password: `secure123`
     - Role: `cashier`
     - Name: `John Doe`

4. **System Actions**
   - ✅ Validates admin permissions
   - ✅ Stores user in `.env` file: `USER_AND_PASSWORD_AND_HOST_2=john_doe,secure123,localhost,cashier`
   - ✅ Creates database user: `app_john_doe` with password `secure123_db`
   - ✅ Assigns role: `order_processor_role` to database user
   - ✅ Tests database connection

### User Login Process

1. **Application Login**
   - User enters username/password
   - System validates against `.env` file

2. **Database Connection**
   - System establishes connection using role-based credentials
   - Example: `cashier` role → connects as `app_cashier` user

3. **Role-Based Access**
   - Database queries executed with appropriate permissions
   - UI shows role-specific features

## 🔐 Security Features

### Database Level Security
- Each role has specific table permissions
- Database users cannot access unauthorized data
- Connection pooling per role prevents privilege escalation

### Application Level Security
- JWT tokens for session management
- Role-based middleware protection
- Input validation and sanitization

### Audit Trail
- All user creation/updates logged
- Database connection attempts tracked
- Failed authentication attempts recorded

## 🗂️ Role Permissions

### Admin Role (`admin_role`)
- Full database access
- Can create/modify/delete users
- Can manage all application features

### Manager Role (`inventory_manager_role`)
- Full access to application tables
- Can manage inventory and products
- Can view user information

### Cashier Role (`order_processor_role`)
- Can process orders and payments
- Can manage customer information
- Read-only access to products

### Vendor Role (`warehouse_staff_role`)
- Can manage suppliers and materials
- Can update inventory logs
- Read-only access to products

### Barista/User Role (`support_staff_role`)
- Read-only access to customer data
- Can view orders and products
- Can check loyalty points

## 📋 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user info

### User Management (Admin Only)
- `POST /api/auth/users` - Create new user
- `PUT /api/auth/users/:username` - Update user
- `DELETE /api/auth/users/:username` - Delete user
- `GET /api/auth/users` - List all users

### Example API Response for User Creation

```json
{
  "success": true,
  "message": "User john_doe created successfully in both .env file and database",
  "data": {
    "username": "john_doe",
    "role": "cashier",
    "name": "John Doe",
    "envUser": true,
    "dbUser": true,
    "dbUsername": "app_john_doe",
    "dbRole": "order_processor_role"
  }
}
```

## 🔧 Troubleshooting

### Database Connection Issues

1. **Check Role Connections**
   ```bash
   # Server logs will show connection test results
   🔍 Testing Role-Based Database Connections...
   Testing admin connection...
   ✅ admin database connection successful
   ```

2. **Verify Database Users**
   ```sql
   SELECT User, Host FROM mysql.user WHERE User LIKE 'app_%';
   ```

3. **Check Role Assignments**
   ```sql
   SELECT * FROM mysql.role_edges WHERE FROM_USER LIKE 'app_%';
   ```

### Common Issues

1. **"Database connection failed for your role"**
   - Check if database user exists
   - Verify role assignments
   - Ensure SSL certificate is available

2. **"Only administrators can create users"**
   - Login as admin user
   - Check user role in `.env` file

3. **"User already exists"**
   - Username must be unique
   - Check existing users in `.env` file

## 📊 Monitoring

The system provides detailed logging for:
- User creation/updates/deletions
- Database connection attempts
- Authentication failures
- Role-based access attempts

Check server logs for monitoring information:
```bash
# Server startup logs
🚀 Server running on port 3000
✅ All role-based database connections established successfully

# User management logs
🔐 Creating user: john_doe with role: cashier (requested by: admin)
✅ User creation successful: john_doe
```

## 🔄 Maintenance

### Regular Tasks

1. **Monitor Database Connections**
   - Check connection pool status
   - Monitor failed connection attempts

2. **User Management**
   - Review user access patterns
   - Remove inactive users
   - Update user permissions as needed

3. **Security Audits**
   - Review role permissions
   - Check for unauthorized access attempts
   - Update passwords regularly

### Backup Considerations

- Backup `.env` file (contains user data)
- Backup database users and roles
- Document custom role configurations

## 🎯 Best Practices

1. **Strong Passwords**
   - Enforce minimum password length
   - Use complex passwords for database users

2. **Role Management**
   - Use least privilege principle
   - Regularly review role permissions

3. **Connection Management**
   - Monitor connection pool usage
   - Implement connection timeouts

4. **Logging and Monitoring**
   - Enable audit logging
   - Monitor failed authentication attempts
   - Set up alerts for security events

This dual authentication system provides enterprise-level security while maintaining ease of use for administrators and end users.
