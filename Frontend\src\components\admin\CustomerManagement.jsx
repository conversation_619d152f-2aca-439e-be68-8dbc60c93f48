import React, { useState } from 'react';
import { api } from '../../services/api.js';
import DataTable from '../common/DataTable';

const CustomerManagement = () => {
    const [selectedCustomer, setSelectedCustomer] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState('view'); // view, edit, create

    const columns = [
        {
            key: 'customer_id',
            label: 'ID',
            sortable: true
        },
        {
            key: 'customer_name',
            label: 'Name',
            sortable: true
        },
        {
            key: 'email',
            label: 'Email',
            sortable: true
        },
        {
            key: 'phone',
            label: 'Phone',
            sortable: true
        },
        {
            key: 'address',
            label: 'Address',
            sortable: false
        },
        {
            key: 'created_at',
            label: 'Created',
            sortable: true,
            render: (value) => new Date(value).toLocaleDateString()
        }
    ];

    const actions = [
        {
            label: 'View',
            onClick: (customer) => {
                setSelectedCustomer(customer);
                setModalType('view');
                setShowModal(true);
            },
            className: 'text-blue-600 hover:text-blue-900'
        },
        {
            label: 'Edit',
            onClick: (customer) => {
                setSelectedCustomer(customer);
                setModalType('edit');
                setShowModal(true);
            },
            className: 'text-green-600 hover:text-green-900'
        },
        {
            label: 'Delete',
            onClick: (customer) => {
                if (window.confirm(`Are you sure you want to delete customer ${customer.customer_name}?`)) {
                    handleDeleteCustomer(customer.customer_id);
                }
            },
            className: 'text-red-600 hover:text-red-900'
        }
    ];

    const handleDeleteCustomer = async (customerId) => {
        try {
            await api.customers.delete(customerId);
            // Refresh the table data
            window.location.reload();
        } catch (error) {
            console.error('Error deleting customer:', error);
            alert('Failed to delete customer');
        }
    };

    const fetchCustomers = async (params) => {
        try {
            const response = await api.customers.getAll(params);
            return response;
        } catch (error) {
            console.error('Error fetching customers:', error);
            throw error;
        }
    };

    const CustomerModal = () => {
        if (!showModal) return null;

        return (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div className="mt-3">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">
                                {modalType === 'view' ? 'Customer Details' : 
                                 modalType === 'edit' ? 'Edit Customer' : 'Create Customer'}
                            </h3>
                            <button
                                onClick={() => setShowModal(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        {selectedCustomer && (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Name</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedCustomer.customer_name}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Email</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedCustomer.email}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedCustomer.phone}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Address</label>
                                    <div className="mt-1 text-sm text-gray-900">{selectedCustomer.address}</div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Created</label>
                                    <div className="mt-1 text-sm text-gray-900">
                                        {new Date(selectedCustomer.created_at).toLocaleString()}
                                    </div>
                                </div>
                            </div>
                        )}
                        
                        <div className="mt-6 flex justify-end space-x-3">
                            <button
                                onClick={() => setShowModal(false)}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">Customer Management</h2>
                <button
                    onClick={() => {
                        setSelectedCustomer(null);
                        setModalType('create');
                        setShowModal(true);
                    }}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                >
                    Add Customer
                </button>
            </div>

            <DataTable
                title="Customers"
                columns={columns}
                fetchData={fetchCustomers}
                actions={actions}
                searchPlaceholder="Search customers..."
                defaultSort={{ field: 'customer_id', order: 'desc' }}
            />

            <CustomerModal />
        </div>
    );
};

export default CustomerManagement;
