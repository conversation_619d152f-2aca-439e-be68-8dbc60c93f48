-- Create the auto-insert loyalty points trigger
DELIMITER //

CREATE TRIGGER after_payment_insert
AFTER INSERT ON Payments
FOR EACH ROW
BEGIN
    DECLARE order_qty INT;

    -- Get quantity from order
    SELECT quantity INTO order_qty
    FROM orders
    WHERE order_id = NEW.order_id;

    -- Insert loyalty points
    INSERT INTO Loyalty_Points (order_id, points_earned)
    VALUES (NEW.order_id, order_qty);
END;
//

DELIMITER ;

-- Test the trigger by inserting a payment
-- This should automatically create a loyalty point record
INSERT INTO Payments (order_id, amount, payment_method, payment_date) 
VALUES (1, 50.00, 'credit_card', NOW());

-- Check if loyalty points were created
SELECT * FROM Loyalty_Points WHERE order_id = 1;
