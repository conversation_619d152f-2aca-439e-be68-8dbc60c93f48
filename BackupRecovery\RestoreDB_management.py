import subprocess
import os
from dotenv import load_dotenv
load_dotenv()

host = os.getenv("DB_HOST")
user = os.getenv("DB_USER")
password = os.getenv("DB_PASSWORD")

def restore_database(host, user, password, db_name, input_file):
    command = [
        "mysql",
        f"--host={host}",
        f"--user={user}",
        f"--password={password}",
        db_name
    ]
    print(command)
    
    with open(input_file, "r") as infile:
        result = subprocess.run(command, stdin=infile, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    print(result)
    
    if result.returncode == 0:
        print(f"Restore successful from {input_file}.")
    else:
        print(f"Error: {result.stderr.decode('utf-8')}")

# Example usage
restore_database(host, user, password, "project_database_management", "project_database_management_backup.sql")
restore_database(host, user, password, "project_database_management1", "project_database_management1_backup.sql")
restore_database(host, user, password, "project_database_management2", "project_database_management2_backup.sql")