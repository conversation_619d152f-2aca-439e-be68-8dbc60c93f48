import AuthService from '../services/authService.js';
import { connectToAivenDB } from '../config/RoleBasedDBConnection.js';

/**
 * Middleware to authenticate token and provide database connection
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Access token required'
            });
        }

        const session = AuthService.verifyToken(token);
        if (!session) {
            return res.status(401).json({
                success: false,
                message: 'Invalid or expired token'
            });
        }

        // Add user info to request object
        req.user = session;
        
        // Add database connection method to request
        req.getDbConnection = async () => {
            return await AuthService.getDatabaseConnection(token);
        };

        next();
    } catch (error) {
        console.error('Authentication middleware error:', error);
        res.status(500).json({
            success: false,
            message: 'Authentication failed',
            error: error.message
        });
    }
};

/**
 * Middleware to ensure user has admin role
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const requireAdmin = (req, res, next) => {
    if (!req.user || req.user.role !== 'admin') {
        return res.status(403).json({
            success: false,
            message: 'Admin access required'
        });
    }
    next();
};

/**
 * Middleware to ensure user has manager role or higher
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const requireManager = (req, res, next) => {
    if (!req.user || !['admin', 'manager'].includes(req.user.role)) {
        return res.status(403).json({
            success: false,
            message: 'Manager access required'
        });
    }
    next();
};

/**
 * Middleware to ensure user has cashier role or higher
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const requireCashier = (req, res, next) => {
    if (!req.user || !['admin', 'manager', 'cashier'].includes(req.user.role)) {
        return res.status(403).json({
            success: false,
            message: 'Cashier access required'
        });
    }
    next();
};

/**
 * Middleware to provide role-based database connection
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const withRoleBasedDB = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        // Create database connection based on user role
        const dbConnection = await connectToAivenDB(req.user.role);
        
        // Add connection to request object
        req.dbConnection = dbConnection;
        
        // Add cleanup function
        req.cleanupDb = () => {
            if (req.dbConnection) {
                req.dbConnection.release();
            }
        };

        // Ensure connection is released after response
        const originalEnd = res.end;
        res.end = function(...args) {
            if (req.dbConnection) {
                req.dbConnection.release();
            }
            originalEnd.apply(this, args);
        };

        next();
    } catch (error) {
        console.error('Database connection middleware error:', error);
        res.status(500).json({
            success: false,
            message: 'Database connection failed',
            error: error.message
        });
    }
};

/**
 * Middleware to log user actions for audit purposes
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const logUserAction = (req, res, next) => {
    if (req.user) {
        console.log(`📝 User Action: ${req.user.username} (${req.user.role}) - ${req.method} ${req.originalUrl}`);
    }
    next();
};

export default {
    authenticateToken,
    requireAdmin,
    requireManager,
    requireCashier,
    withRoleBasedDB,
    logUserAction
};
