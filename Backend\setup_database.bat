@echo off
echo ======================================
echo Database Setup Script
echo ======================================
echo.
echo This script will help you set up the database roles and users
echo for the dual authentication system.
echo.

set /p host="Enter your Aiven MySQL host: "
set /p port="Enter your Aiven MySQL port (default 3306): "
set /p admin_user="Enter your admin username: "
set /p database="Enter your database name: "

if "%port%"=="" set port=3306

echo.
echo Connecting to MySQL and running setup script...
echo.

mysql -h %host% -P %port% -u %admin_user% -p --ssl-ca=ca.pem %database% < sql/setup_database_roles.sql

if %errorlevel% equ 0 (
    echo.
    echo ✅ Database setup completed successfully!
    echo.
    echo Next steps:
    echo 1. Update your .env file with the database credentials
    echo 2. Start the backend server: npm start
    echo 3. <PERSON><PERSON> as admin (username: admin, password: admin123)
    echo 4. Create additional users through the User Management interface
    echo.
) else (
    echo.
    echo ❌ Database setup failed. Please check:
    echo - Database connection details
    echo - SSL certificate (ca.pem) in the Backend folder
    echo - Admin user permissions
    echo.
)

pause
